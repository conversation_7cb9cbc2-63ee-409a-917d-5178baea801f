############################################
# Dockerfile to build elasticsearch_service microservice
# Based on python ECR hub image
############################################

# Set base image to python
FROM 078398740737.dkr.ecr.us-west-2.amazonaws.com/repository-h1fytp6aeclu:07a51f81

# File Author / Maintainer
LABEL maintainer="Shamsher Kushwaha <<EMAIL>>"

# Copy source file and python req's
COPY . /usr/src/
COPY app/requirements.txt /
RUN rm -rf /usr/src/app/.env 
COPY config/.env.prod /usr/src/app/.env 

# Install requirements
WORKDIR /usr/src/app/
RUN apt update
RUN apt install awscli nginx -y
RUN pip install env 
RUN python -m venv env
RUN pip install -r requirements.txt

# Setup nginx
RUN rm /etc/nginx/sites-enabled/default
ADD config/flask.conf /etc/nginx/sites-available/
RUN ln -s /etc/nginx/sites-available/flask.conf /etc/nginx/sites-enabled/flask.conf
RUN echo "daemon off;" >> /etc/nginx/nginx.conf


# Setup supervisord
RUN mkdir -p /usr/local/etc/supervisord/{conf-available,conf-enabled}
RUN mkdir /var/log/supervisord
RUN mkdir /var/run/supervisord
ADD config/gunicorn_logging.conf .
ADD config/supervisord.conf /usr/local/etc/supervisord.conf
RUN mkdir -p /var/log/supervisor
ADD config/nginx-supervisord.conf /usr/local/etc/supervisord/conf-enabled/
ADD config/gunicorn.conf /usr/local/etc/supervisord/conf-enabled/

ADD config/initial.sh /usr/bin/initial
RUN chmod +x /usr/bin/initial
ENTRYPOINT [ "initial" ]
