# csv2_product_detail_fastapi

A web application fo processing and serving product details from CSV files using FasAPI.

## Table

- [Overview](#overview)
- [Features](#features)
- [Project Structure](#project-structure)
- [Installation](#installation)
- [Configuration](#configuration)
- [Usage](#usage)
- [API Endpoints](#api-endpoints)
- [Testing](#testing)
- [Deployment](#deployment)
- [License](#license)

## Overview

This application processes product details from CSV files from and exposes APIs for querying and serving the data.

## Features

- CSV file ingestion processes product details from CSV files.
- RESTful API endpoints for product details
- Environment-based configuration
- Logging and error handling
- Docker support for production UAT

## Project Structure

```
.
├── app/
│   ├── app.py                # Main application entry point
│   ├── config/               # Configuration files
│   ├── constant/             # Constant values
│   ├── db/                   # Database-related code
│   ├── env/                  # Environment variable management
│   ├── externalservices/     # Integrations with external services
│   ├── logs/                 # Log files
│   ├── middlewares/          # Middleware components
│   ├── pydantic_models/      # Pydantic models for validation
│   ├── query_functions/      # Query logic
│   ├── routing/              # API route definitions
│   ├── services/             # Business logic/services
│   ├── static/               # Static files
│   └── utils/                # Utility functions
├── config/                   # Deployment and server configs
├── script/                   # Deployment scripts
├── requirements.txt          # Python dependencies
├── Prod.Dockerfile           # Dockerfile for production
├── Uat.Dockerfile            # Dockerfile for UAT
└── README.md                 # Project documentation
```

## Installation

1. Clone the repository:
    ```sh
    git clone <repo-url>
    cd csv2_product_detail_fastapi
    ```
2. Create a virtual environment and activate it:
    - For Windows:
        ```sh
        python -m venv env or conda create -n env python=python_version
        env\Scripts\activate or conda activate env
        ```
    - For Unix/MacOS:
        ```sh
        python3 -m venv env or conda create -n env python=python_version
        source env/bin/activate or conda activate env
        ```
3. Install dependencies:
    ```sh
    pip install -r requirements.txt
    ```
4. Set up environment variables:
    ```sh
    cp app/.env.example app/.env


## Configuration

- Copy `.env` files are needed and update environment variables as needed.
- COnfiguration files are located in the `config/` and `app/config/` directories.

## Usage

To run the application locally:

```sh
cd app
uvicorn app:app --reload
```

## API Endpoints

Document your main API endpoints here.

- `POST /data/productdata`: Retrieves product details.
- `POST /data/affinities`: Retrieves affinity details.
- `POST /data/tracking`: Retrieves tracking details.

## Testing



## Deployment

For production and UAT, Dockerfiles are provided. Build and run the Docker containers as needed.


    