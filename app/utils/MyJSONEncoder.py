from fastapi.encoders import jsonable_encoder
from decimal import Decimal
from json import JSONEncoder

class MyJSONEncoder(JSONEncoder):
    def default(self, obj):
        if isinstance(obj, Decimal):
            # Convert decimal instances to strings.
            return str(obj)
        return super(<PERSON><PERSON><PERSON><PERSON>nco<PERSON>, self).default(obj)
# import decimal
# import flask.json


# class MyJSONEncoder(flask.json.JSONEncoder):

#     def default(self, obj):
#         if isinstance(obj, decimal.Decimal):
#             # Convert decimal instances to strings.
#             return str(obj)
#         return super(<PERSON><PERSON><PERSON><PERSON>nco<PERSON>, self).default(obj)