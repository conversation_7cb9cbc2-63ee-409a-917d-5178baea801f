import os
import aioredis
# from asyncio_redis import ConnectionPool
from constant.constant import *
from utils.Logger import Logger
import asyncio
import aiomysql
from fastapi import HTTPException
import ssl

SSL_CA=os.getenv('GLOBAL_PEM_PATH')


"""
The purpose of 'QueryCache' class is to cache the results of a purticular query.
Entry point of the class is 'query' method. Parameters are:
query=select columnname from tablename
conn=mysql connection object
cache=False # True for cache the query vise-versa
fetch_all=False # True for fetching all records and vise-versa
"""

class QueryCache:
    def __init__(self):
        self._query = None
        self._conn = None
        self._fetch_all = False
        self.lock = asyncio.Lock()
        self.logger = Logger(self.__class__.__name__).get()

    async def query(self, query, conn, fetch_all=False, params=None):
        data = []
        self._query = query

        async with self.lock:  # Use a lock to prevent concurrent access to the same connection
            try:
                if conn is None:
                    raise Exception("Database connection is not initialized")

                async with conn.cursor() as cursor:
                    await cursor.execute(self._query, params)

                    if fetch_all:
                        rows = await cursor.fetchall()
                    else:
                        rows = [await cursor.fetchone()]

                    column_names = [col[0] for col in cursor.description]
                    data = [dict(zip(column_names, row)) for row in rows]

                return data
            except Exception as e:
                # Log the exception and handle it gracefully
                self.logger.error(f"Database query error: {str(e)}")
                return data
        
    async def get_redis(self):
        try:
            # Create a connection to the Redis server
            redis = await aioredis.create_redis_pool(
                address=(REDIS_HOST,REDIS_PORT)
            )
            return redis
        except Exception as e:
            raise e

    async def delete_key_from_redis_cache(self,key):
        try:
            # Connect to the Redis server
            redis = await aioredis.create_redis_pool(
                address=(REDIS_HOST,REDIS_PORT))

            # Delete the specified key from the Redis cache   
            result = await redis.delete(key)

            # Check if the key was successfully deleted
            if result == 1:
                return f"Key '{key}' deleted successfully from the Redis cache."
            else:
                return f"Key '{key}' not found in the Redis cache."

        except Exception as e:
            return f"Error: {str(e)}"

        finally:
            # Close the Redis connection
            redis.close()
            await redis.wait_closed()

class Db(QueryCache):
    __conn = None
    
    async def conn_competi_db(self):
        if self.__conn is None:
            try:
                self.__conn = await self._connect_competi_db()
            except Exception as e:
                raise HTTPException(status_code=500, detail="Database connection error")
        return self.__conn

    async def _connect_competi_db(self):
        ssl_context = ssl.create_default_context(cafile=SSL_CA)
        ssl_context.check_hostname = True
        connection = await aiomysql.connect(
            host=os.environ['HISTORICAL_TLS_HOST'],
            db=os.getenv('HISTORICAL_MASTER_LOOKUP'),
            user=os.environ['HISTORICAL_TLS_USERNAME'],
            password=os.environ['HISTORICAL_TLS_PASSWORD'],
            ssl=ssl_context
           
        )
        return connection


class Db_mobiledb:

    def __init__(self):
        self.__mobile_digital_conn = None

    async def conn_csv2_mobile_digital_dbase(self):
        if self.__mobile_digital_conn is None:
            try:
                self.__mobile_digital_conn = await self.connect_csv2_mobile_digital_dbase()
            except Exception as e:
                raise HTTPException(status_code=500, detail="Database connection error")
        return self.__mobile_digital_conn

    async def connect_csv2_mobile_digital_dbase(self):
        try:
            ssl_context = ssl.create_default_context(cafile=SSL_CA)
            ssl_context.check_hostname = True
            connection = await aiomysql.connect(
                host=os.environ['HISTORICAL_TLS_HOST'],
                db=os.getenv('HISTORICAL_MOBILE_DIGITAL'),
                user=os.environ['HISTORICAL_TLS_USERNAME'],
                password=os.environ['HISTORICAL_TLS_PASSWORD'],
                ssl=ssl_context
            )
            return connection
        except Exception as e:
            raise HTTPException(status_code=500, detail="Database connection error")

class Db_productdb:

    def __init__(self):
        self.__cs_conn = None

    async def conn_csv2_product_dbase(self):
        if self.__cs_conn is None:
            try:
                self.__cs_conn = await self.connect_csv2_product_dbase()
            except Exception as e:
                raise HTTPException(status_code=500, detail="Database connection error")
        return self.__cs_conn

    async def connect_csv2_product_dbase(self):
        try:
            ssl_context = ssl.create_default_context(cafile=SSL_CA)
            ssl_context.check_hostname = True
            connection = await aiomysql.connect(
                host=os.environ['HISTORICAL_TLS_HOST'],
                db=os.getenv('HISTORICAL_CSV2_PRODUCT'),
                user=os.environ['HISTORICAL_TLS_USERNAME'],
                password=os.environ['HISTORICAL_TLS_PASSWORD'],
                ssl=ssl_context
            )
            return connection
        except Exception as e:
            raise HTTPException(status_code=500, detail="Database connection error")
        

# class Db_contentsite:
#     __contentsite = None
    
#     async def conn_contentsite_db(self):
#         if self.__contentsite is None:
#             try:
#                 self.__contentsite = await self._connect_contentsite_db()
#             except Exception as e:
#                 raise HTTPException(status_code=500, detail="Database connection error")
#         return self.__contentsite

#     async def _connect_contentsite_db(self):
#         connection = await aiomysql.connect(
#             host=os.environ['HISTORICAL_HOST'],
#             db=os.getenv('HISTORICAL_CSV2_CONTENTSITE'),
#             user=os.environ['HISTORICAL_USERNAME'],
#             password=os.environ['HISTORICAL_PASSWORD']
#         )
#         return connection
