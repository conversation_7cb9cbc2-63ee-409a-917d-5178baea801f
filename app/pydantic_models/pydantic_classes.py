from services.rawdata.model import Model
from pydantic import BaseModel
import pandas as pd
import re
from typing import Optional


class MortgageLoan(BaseModel):
    MaximumLoanAmount: Optional[float]
    MinimumLoanAmount: Optional[float]
    OfferedLoanAmount: Optional[float]
    UpperAPR: Optional[float]
    LowerAPR: Optional[float]
    OfferedAPR: Optional[float]
    LoanTerm: Optional[int]
    MLApplicationType: Optional[str]
    RateType: Optional[str]
    fha: Optional[str]
    IntroductoryAPR:Optional[float]
    IntroductoryPeriod:Optional[int]
    refinance:Optional[str]

    @classmethod
    async def from_csv2_product_data(cls, csv2_product_data:pd.DataFrame,master_lookup_conn):
        MortgageLoan_data = {
            'MaximumLoanAmount': None,
            'MinimumLoanAmount': None,
            'OfferedLoanAmount': None,
            'UpperAPR': None,
            'LowerAPR': None,
            'LoanTerm': None,
            'MLApplicationType': None,
            'fha': None,
            'IntroductoryAPR': None,
            'IntroductoryPeriod': None,
            'refinance': None
        }
        if 'mortgage_loan' in csv2_product_data and csv2_product_data.at[0, 'mortgage_loan'] != 0:
            model=Model()
            appplication_data=await model.getapplicationType(csv2_product_data['MLApplicationType'].unique()[0],master_lookup_conn)
            application_type = appplication_data.get('ApplicationTypeName') if appplication_data and 'ApplicationTypeName' in appplication_data else ''
            cashRate=await model.getrateType(csv2_product_data['RateType'].unique()[0],master_lookup_conn)
            cashRateType = cashRate.get('RateTypeName') if cashRate and 'RateTypeName' in cashRate else ''
            MortgageLoan_data.update({
                'MaximumLoanAmount':csv2_product_data['MaximumLoanAmount'].unique()[0],
                'MinimumLoanAmount':csv2_product_data['MinimumLoanAmount'].unique()[0],
                'OfferedLoanAmount':csv2_product_data['OfferedLoanAmount'].unique()[0],
                'UpperAPR':csv2_product_data['UpperAPR'].unique()[0],
                'LowerAPR':csv2_product_data['LowerAPR'].unique()[0],
                'OfferedAPR':csv2_product_data['OfferedAPR'].unique()[0],
                'LoanTerm':csv2_product_data['LoanTerm'].unique()[0],
                'MLApplicationType':application_type,
                'RateType':cashRateType,
                'fha':'No' if csv2_product_data['fha'].unique()[0] == 0 else 'Yes',
                'IntroductoryAPR':csv2_product_data['IntroductoryAPR'].unique()[0],
                'IntroductoryPeriod':csv2_product_data['IntroductoryPeriod'].unique()[0],
                'refinance':'No' if csv2_product_data['refinance'].unique()[0] == 0 else 'Yes'
            })
        return cls(**MortgageLoan_data)

class PaymentCardDetails(BaseModel):
    ApplicationType: Optional[str]
    CardNetwork: Optional[str]
    RewardsProgram: Optional[str]
    RewardsProgramEmphasis:Optional[str]
    RewardsRate:Optional[str]
    CardLevel: Optional[str]
    PurchaseRegularAPR: Optional[float]
    Tier2PurchaseRegularAPR: Optional[float]
    Tier3PurchaseRegularAPR: Optional[float]
    PurchaseRateType: Optional[str]
    BalanceTransferRegularAPR: Optional[float]
    Tier2BalanceTransferRegularAPR: Optional[float]
    Tier3BalanceTransferRegularAPR: Optional[float]
    BalanceTransferRateType: Optional[str]
    CashAdvanceRegularAPR: Optional[float]
    Tier2CashAdvanceRegularAPR: Optional[float]
    Tier3CashAdvanceRegularAPR: Optional[float]
    CashAdvanceRateType: Optional[str]
    AnnualFee: Optional[float]
    Tier1AnnualFee: Optional[float]
    Tier2AnnualFee: Optional[float]
    LateFee: Optional[float]
    Tier1LateFee: Optional[float]
    Tier2LateFee: Optional[float]
    OverlimitFee: Optional[float]
    Tier1OverlimitFee: Optional[float]
    Tier2OverlimitFee: Optional[float]
    BalanceTransferUsageFee: Optional[float]
    BalanceTransferMinimumFee: Optional[float]
    BalanceTransferMaximumFee: Optional[float]
    CashAdvanceUsageFee: Optional[float]
    CashAdvanceMinimumFee: Optional[float]
    CashAdvanceMaximumFee: Optional[float]
    MinimumCardLimit: Optional[float]
    MaximumCardLimit: Optional[float]
    PurchaseIntroductoryAPR: Optional[float]
    PurchaseIntroductoryPeriod: Optional[int]
    BalanceTransferIntroductoryAPR: Optional[float]
    BalanceTransferIntroductoryPeriod: Optional[int]
    BalanceTransferIntroductoryUsageFee: Optional[float]
    BalanceTransferIntroductoryMinimumFee: Optional[float]
    BalanceTransferIntroductoryMaximumFee: Optional[float]
    BalanceTransferIntroductoryFeePeriod: Optional[int]
    CashAdvanceIntroductoryAPR: Optional[float]
    CashAdvanceIntroductoryPeriod: Optional[int]
    CashAdvanceIntroductoryUsageFee: Optional[float]
    CashAdvanceIntroductoryMinimumFee: Optional[float]
    CashAdvanceIntroductoryMaximumFee: Optional[float]
    CashAdvanceIntroductoryFeePeriod: Optional[int]
    PromotionalPurchasePricingUsageOfferPercent: Optional[float]
    PromotionalPurchasePricingUsageOfferMonth: Optional[int]
    MonthlyMaintenanceFee: Optional[float]
    StandardMonthlyMaintenanceFee: Optional[float]


    @classmethod
    async def from_csv2_product_data(cls, csv2_product_data:pd.DataFrame,master_lookup_conn):
        payment_data = {
            'ApplicationType': None,
            'CardNetwork': None,
            'RewardsProgram': None,
            'RewardsProgramEmphasis':None,
            'RewardsRate':None,
            'CardLevel': None,
            'PurchaseRegularAPR': None,
            'Tier2PurchaseRegularAPR': None,
            'Tier3PurchaseRegularAPR': None,
            'PurchaseRateType': None,
            'BalanceTransferRegularAPR': None,
            'Tier2BalanceTransferRegularAPR': None,
            'Tier3BalanceTransferRegularAPR': None,
            'BalanceTransferRateType': None,
            'CashAdvanceRegularAPR': None,
            'Tier2CashAdvanceRegularAPR': None,
            'Tier3CashAdvanceRegularAPR': None,
            'CashAdvanceRateType': None,
            'AnnualFee': None,
            'Tier1AnnualFee': None,
            'Tier2AnnualFee': None,
            'LateFee': None,
            'Tier1LateFee': None,
            'Tier2LateFee': None,
            'OverlimitFee': None,
            'Tier1OverlimitFee': None,
            'Tier2OverlimitFee': None,
            'BalanceTransferUsageFee': None,
            'BalanceTransferMinimumFee': None,
            'BalanceTransferMaximumFee': None,
            'CashAdvanceUsageFee': None,
            'CashAdvanceMinimumFee': None,
            'CashAdvanceMaximumFee': None,
            'MinimumCardLimit': None,
            'MaximumCardLimit': None,
            'PurchaseIntroductoryAPR': None,
            'PurchaseIntroductoryPeriod': None,
            'BalanceTransferIntroductoryAPR': None,
            'BalanceTransferIntroductoryPeriod': None,
            'BalanceTransferIntroductoryUsageFee': None,
            'BalanceTransferIntroductoryMinimumFee': None,
            'BalanceTransferIntroductoryMaximumFee': None,
            'BalanceTransferIntroductoryFeePeriod': None,
            'CashAdvanceIntroductoryAPR': None,
            'CashAdvanceIntroductoryPeriod': None,
            'CashAdvanceIntroductoryUsageFee': None,
            'CashAdvanceIntroductoryMinimumFee': None,
            'CashAdvanceIntroductoryMaximumFee': None,
            'CashAdvanceIntroductoryFeePeriod': None,
            'PromotionalPurchasePricingUsageOfferPercent': None,
            'PromotionalPurchasePricingUsageOfferMonth': None,
            'MonthlyMaintenanceFee':None,
            'StandardMonthlyMaintenanceFee':None

        }
        if 'payment' in csv2_product_data and csv2_product_data.at[0, 'payment'] != 0:
            model=Model()
            card_network=await model.getcardDetails(csv2_product_data['CardNetwork'].unique(),master_lookup_conn)
            card_type = ', '.join(card_network) if card_network is not None and len(card_network) > 0 else ''
            
            appplication_data=await model.getapplicationType(csv2_product_data['ApplicationType'].unique()[0],master_lookup_conn)
            application_type = appplication_data.get('ApplicationTypeName') if appplication_data and 'ApplicationTypeName' in appplication_data else ''
            
            card_level=await model.getcardLevelType(csv2_product_data['CardLevel'].unique(),master_lookup_conn)
            cardLevelType = ', '.join(card_level) if card_level is not None and len(card_level) > 0 else ''   

            purchase_rate=await model.getrateType(csv2_product_data['PurchaseRateType'].unique()[0],master_lookup_conn)
            purchaseRateType = purchase_rate.get('RateTypeName') if purchase_rate and 'RateTypeName' in purchase_rate else ''

            balanceRate=await model.getrateType(csv2_product_data['BalanceTransferRateType'].unique()[0],master_lookup_conn)
            balanceRateType = balanceRate.get('RateTypeName') if balanceRate and 'RateTypeName' in balanceRate else ''

            cashRate=await model.getrateType(csv2_product_data['CashAdvanceRateType'].unique()[0],master_lookup_conn)
            cashRateType = cashRate.get('RateTypeName') if cashRate and 'RateTypeName' in cashRate else ''
           
            
            rewardName=await model.getrewardTypeName(csv2_product_data['RewardsProgramEmphasis'].unique()[0],master_lookup_conn)
            rewardTypeName = ""
            if rewardName is not None:
                try:
                    rewardTypeName = [item['RewardTypeName'] for item in rewardName]
                    rewardTypeName = ','.join(rewardTypeName)
                except:
                    rewardTypeName = rewardName["RewardTypeName"]   
            payment_data.update({
            'ApplicationType':application_type,
            'CardNetwork':card_type,
            'CardLevel':cardLevelType,
            'RewardsProgram':'No' if csv2_product_data['RewardsProgram'].unique()[0] == 0 else 'Yes',
            'RewardsProgramEmphasis':rewardTypeName,
            'RewardsRate':csv2_product_data['RewardsRate'].unique()[0],
            'PurchaseRegularAPR':csv2_product_data['PurchaseRegularAPR'].unique()[0],
            'Tier2PurchaseRegularAPR':csv2_product_data['Tier2PurchaseRegularAPR'].unique()[0],
            'Tier3PurchaseRegularAPR':csv2_product_data['Tier3PurchaseRegularAPR'].unique()[0],
            'PurchaseRateType':purchaseRateType,
            'BalanceTransferRegularAPR':csv2_product_data['BalanceTransferRegularAPR'].unique()[0],
            'Tier2BalanceTransferRegularAPR':csv2_product_data['Tier2BalanceTransferRegularAPR'].unique()[0],
            'Tier3BalanceTransferRegularAPR':csv2_product_data['Tier3BalanceTransferRegularAPR'].unique()[0],
            'BalanceTransferRateType':balanceRateType,
            'CashAdvanceRegularAPR':csv2_product_data['CashAdvanceRegularAPR'].unique()[0],
            'Tier2CashAdvanceRegularAPR':csv2_product_data['Tier2CashAdvanceRegularAPR'].unique()[0],
            'Tier3CashAdvanceRegularAPR':csv2_product_data['Tier3CashAdvanceRegularAPR'].unique()[0],
            'CashAdvanceRateType':cashRateType,
            'AnnualFee':csv2_product_data['AnnualFee'].unique()[0],
            'Tier1AnnualFee':csv2_product_data['Tier1AnnualFee'].unique()[0],
            'Tier2AnnualFee':csv2_product_data['Tier2AnnualFee'].unique()[0],
            'LateFee':csv2_product_data['LateFee'].unique()[0],
            'Tier1LateFee':csv2_product_data['Tier1LateFee'].unique()[0],
            'Tier2LateFee':csv2_product_data['Tier2LateFee'].unique()[0],
            'OverlimitFee':csv2_product_data['OverlimitFee'].unique()[0],
            'Tier1OverlimitFee':csv2_product_data['Tier1OverlimitFee'].unique()[0],
            'Tier2OverlimitFee':csv2_product_data['Tier2OverlimitFee'].unique()[0],
            'BalanceTransferUsageFee':csv2_product_data['BalanceTransferUsageFee'].unique()[0],
            'BalanceTransferMinimumFee':csv2_product_data['BalanceTransferMinimumFee'].unique()[0],
            'BalanceTransferMaximumFee':csv2_product_data['BalanceTransferMaximumFee'].unique()[0],
            'CashAdvanceUsageFee':csv2_product_data['CashAdvanceUsageFee'].unique()[0],
            'CashAdvanceMinimumFee':csv2_product_data['CashAdvanceMinimumFee'].unique()[0],
            'CashAdvanceMaximumFee':csv2_product_data['CashAdvanceMaximumFee'].unique()[0],
            'MinimumCardLimit':csv2_product_data['MinimumCardLimit'].unique()[0],
            'MaximumCardLimit':csv2_product_data['MaximumCardLimit'].unique()[0],
            'PurchaseIntroductoryAPR':csv2_product_data['PurchaseIntroductoryAPR'].unique()[0],
            'PurchaseIntroductoryPeriod':csv2_product_data['PurchaseIntroductoryPeriod'].unique()[0],
            'BalanceTransferIntroductoryAPR':csv2_product_data['BalanceTransferIntroductoryAPR'].unique()[0],
            'BalanceTransferIntroductoryPeriod':csv2_product_data['BalanceTransferIntroductoryPeriod'].unique()[0],
            'BalanceTransferIntroductoryUsageFee':csv2_product_data['BalanceTransferIntroductoryUsageFee'].unique()[0],
            'BalanceTransferIntroductoryMinimumFee':csv2_product_data['BalanceTransferIntroductoryMinimumFee'].unique()[0],
            'BalanceTransferIntroductoryMaximumFee':csv2_product_data['BalanceTransferIntroductoryMaximumFee'].unique()[0],
            'BalanceTransferIntroductoryFeePeriod':csv2_product_data['BalanceTransferIntroductoryFeePeriod'].unique()[0],
            'CashAdvanceIntroductoryAPR':csv2_product_data['CashAdvanceIntroductoryAPR'].unique()[0],
            'CashAdvanceIntroductoryPeriod':csv2_product_data['CashAdvanceIntroductoryPeriod'].unique()[0],
            'CashAdvanceIntroductoryUsageFee':csv2_product_data['CashAdvanceIntroductoryUsageFee'].unique()[0],
            'CashAdvanceIntroductoryMinimumFee':csv2_product_data['CashAdvanceIntroductoryMinimumFee'].unique()[0],
            'CashAdvanceIntroductoryMaximumFee':csv2_product_data['CashAdvanceIntroductoryMaximumFee'].unique()[0],
            'CashAdvanceIntroductoryFeePeriod':csv2_product_data['CashAdvanceIntroductoryFeePeriod'].unique()[0],
            'PromotionalPurchasePricingUsageOfferPercent':csv2_product_data['PromotionalPurchasePricingUsageOfferPercent'].unique()[0],
            'PromotionalPurchasePricingUsageOfferMonth':csv2_product_data['PromotionalPurchasePricingUsageOfferMonth'].unique()[0],
            'MonthlyMaintenanceFee':csv2_product_data['MonthlyMaintenanceFee'].unique()[0],
            'StandardMonthlyMaintenanceFee':csv2_product_data['StandardMonthlyMaintenanceFee'].unique()[0]
        })  
        
        return cls(**payment_data)

class SocialMedia(BaseModel):
    Network_Name: str
    No_of_tweets: int
    No_of_followers: int
    No_of_fans: int
    external_link: str
    @classmethod
    def from_csv2_product_data(cls, csv2_product_data:pd.DataFrame):
        if 'external_link' in csv2_product_data:
                # Use a regular expression pattern to extract the network name from the URL
                pattern = r'\/\/(?:www\.)?([a-zA-Z0-9-]+)\.'
                match = re.search(pattern, str(csv2_product_data['external_link']))
                # If a network name is found in the URL, extract it
                network_name = ""
                if match:
                    network_name = match.group(1)


                return cls(
                            Network_Name=network_name,
                            No_of_tweets=csv2_product_data['external_updates'].unique()[0],
                            No_of_followers=csv2_product_data['external_fans'].unique()[0],
                            No_of_fans=csv2_product_data['external_fans'].unique()[0],
                            external_link=csv2_product_data['external_link'].unique()[0]

                )
                
        else:
            pass

class Energy(BaseModel):
    ERateType: Optional[str]
    EOfferPrice: Optional[float]
    ECancelFee: Optional[str]

    @classmethod
    async def from_csv2_product_data(cls, csv2_product_data:pd.DataFrame,master_lookup_conn):
        energy_data={
            'ERateType':None,
            'EOfferPrice':None,
            'ECancelFee':None
        }
        if 'energy' in csv2_product_data and csv2_product_data.at[0, 'energy'] != 0:
            model=Model()
            purchase_rate=await model.getrateType(csv2_product_data['ERateType'].unique()[0],master_lookup_conn)
            EnergyRateType = purchase_rate.get('RateTypeName') if purchase_rate and 'RateTypeName' in purchase_rate else ''
            energy_data.update({
                'ERateType':EnergyRateType,
                'EOfferPrice':csv2_product_data['EOfferPrice'].unique()[0],
                'ECancelFee':'No' if csv2_product_data['ECancelFee'].unique()[0] == 0 else 'Yes',
            })
        return cls(**energy_data)
class Telecom(BaseModel):
    FeaturedPlan: Optional[str]
    ContractRequired: Optional[str]
    internet_speed: Optional[str]
    MonthlyCost: Optional[float]
    LowerFeaturedMonthlyCost: Optional[float]
    UpperFeaturedMonthlyCost: Optional[float]
    ActivationCharge: Optional[float]
    TelecomIntroductoryCost: Optional[float]
    TelecomIntroductoryPeriod: Optional[str]

    @classmethod
    def from_csv2_product_data(cls, csv2_product_data: pd.DataFrame):
        telecom_data = {
            'FeaturedPlan': None,
            'ContractRequired': None,
            'internet_speed': None,
            'MonthlyCost': None,
            'LowerFeaturedMonthlyCost': None,
            'UpperFeaturedMonthlyCost': None,
            'ActivationCharge': None,
            'TelecomIntroductoryCost': None,
            'TelecomIntroductoryPeriod': None
        }

        if 'telecom' in csv2_product_data and csv2_product_data.at[0, 'telecom'] != 0:
            telecom_data.update({
                'FeaturedPlan': 'Yes' if csv2_product_data.at[0, 'FeaturedPlan'] != 0 else 'No',
                'ContractRequired': 'Yes' if csv2_product_data.at[0, 'ContractRequired'] != 0 else 'No',
                'internet_speed': str(csv2_product_data.at[0, 'internet_speed']),
                'MonthlyCost': csv2_product_data.at[0, 'MonthlyCost'],
                'LowerFeaturedMonthlyCost': csv2_product_data.at[0, 'LowerFeaturedMonthlyCost'],
                'UpperFeaturedMonthlyCost': csv2_product_data.at[0, 'UpperFeaturedMonthlyCost'],
                'ActivationCharge': csv2_product_data.at[0, 'ActivationCharge'],
                'TelecomIntroductoryCost': csv2_product_data.at[0, 'TelecomIntroductoryCost'],
                'TelecomIntroductoryPeriod': csv2_product_data.at[0, 'TelecomIntroductoryPeriod']
            })

        return cls(**telecom_data)                
# class Telecom(BaseModel):
#     FeaturedPlan: str
#     ContractRequired: str
#     internet_speed: str
#     MonthlyCost: float
#     LowerFeaturedMonthlyCost: float
#     UpperFeaturedMonthlyCost: float
#     ActivationCharge: float
#     TelecomIntroductoryCost: float
#     TelecomIntroductoryPeriod:str

#     @classmethod
#     def from_csv2_product_data(cls, csv2_product_data:pd.DataFrame):
#         if 'telecom' in csv2_product_data and csv2_product_data['telecom'].iloc[0] != 0:

#             return cls(
#                 FeaturedPlan='No' if csv2_product_data['FeaturedPlan'].unique()[0] == 0 else 'Yes',
#                 ContractRequired='No' if csv2_product_data['ContractRequired'].unique()[0] == 0 else 'Yes',
#                 internet_speed=str(csv2_product_data['internet_speed'].unique()[0]),
#                 MonthlyCost=csv2_product_data['MonthlyCost'].unique()[0],
#                 LowerFeaturedMonthlyCost=csv2_product_data['LowerFeaturedMonthlyCost'].unique()[0],
#                 UpperFeaturedMonthlyCost=csv2_product_data['UpperFeaturedMonthlyCost'].unique()[0],
#                 ActivationCharge=csv2_product_data['ActivationCharge'].unique()[0],
#                 TelecomIntroductoryCost=csv2_product_data['TelecomIntroductoryCost'].unique()[0],
#                 TelecomIntroductoryPeriod=str(csv2_product_data['TelecomIntroductoryPeriod'].unique()[0])
#             )   
#         else:
#             return cls(
#                 FeaturedPlan=None,
#                 ContractRequired=None,
#                 internet_speed=None,
#                 MonthlyCost=None,
#                 LowerFeaturedMonthlyCost=None,
#                 UpperFeaturedMonthlyCost=None,
#                 ActivationCharge=None,
#                 TelecomIntroductoryCost=None,
#                 TelecomIntroductoryPeriod=None
#             ) 


class Travel(BaseModel):
    TLCardNetwork: Optional[str]
    TLCreditCardMentioned: Optional[str]
    TLDebitCardMentioned: Optional[str]

    @classmethod
    async def from_csv2_product_data(cls, csv2_product_data:pd.DataFrame,master_lookup_conn):
        travel_data={
            'TLCardNetwork':None,
            'TLCreditCardMentioned':None,
            'TLDebitCardMentioned':None
        }
        if 'travel' in csv2_product_data and csv2_product_data.at[0, 'travel'] != 0:
            model=Model()
            card_network= await model.getcardDetails(csv2_product_data['TLCardNetwork'].unique(),master_lookup_conn)
            card_type = ', '.join(card_network) if card_network is not None and len(card_network) > 0 else ''
            
            travel_data.update({
                'TLCardNetwork':card_type,
                'TLCreditCardMentioned':'No' if csv2_product_data['TLCreditCardMentioned'].unique()[0] == 0 else 'Yes',
                'TLDebitCardMentioned':'No' if csv2_product_data['TLDebitCardMentioned'].unique()[0] == 0 else 'Yes',
            })    
        return cls(**travel_data)

class Banking(BaseModel):
    DebitCardMentioned: Optional[str]
    MinimumDeposit: Optional[float]
    BankingCardType: Optional[str]
    BankingRewardsProgram:Optional[str]
    BankingRewardsProgramEmphasis:Optional[str]

    @classmethod
    async def from_csv2_product_data(cls, csv2_product_data:pd.DataFrame,rawdata,master_lookup_conn):
        banking_data={
            'DebitCardMentioned':None,
            'MinimumDeposit':None,
            'BankingCardType':None,
            'BankingRewardsProgram':None,
            'BankingRewardsProgramEmphasis':None
        }
        if 'banking' in csv2_product_data and csv2_product_data.at[0, 'banking'] != 0:
            model=Model()
            rewardName=await model.getrewardTypeName(csv2_product_data['BankingRewardsProgramEmphasis'].unique()[0],master_lookup_conn)
            rewardTypeName = ""
            if rewardName is not None:
                try:
                    rewardTypeName = [item['RewardTypeName'] for item in rewardName]
                    rewardTypeName = ','.join(rewardTypeName)
                except:
                    rewardTypeName = rewardName["RewardTypeName"]   

            banking_card_type = rawdata.get('CardTypeName', '') if 'CardTypeName' in rawdata else ''
            # Check if 'CardTypeName' is a valid string
            if not isinstance(banking_card_type, str):
                # Handle the case where 'CardTypeName' is not a valid str
                banking_card_type = ''
                
            banking_data.update({
                'DebitCardMentioned':'No' if csv2_product_data['DebitCardMentioned'].unique()[0] == 0 else 'Yes',
                'MinimumDeposit':csv2_product_data['MinimumDeposit'].unique()[0],
                'BankingCardType':banking_card_type,
                'BankingRewardsProgram':'No' if csv2_product_data['BankingRewardsProgram'].unique()[0] == 0 else 'Yes',
                'BankingRewardsProgramEmphasis':rewardTypeName,
            })      
        return cls(**banking_data)

class CreditCardsAccess(BaseModel):
    PromotionalOffer: Optional[str]
    PromotionalOfferAPR: Optional[float]
    BalanceTransferIntroductoryPeriod_CAC1: Optional[int]
    BalanceTransferIntroductoryAPRDetail_CAC:Optional[float]
    BalanceTransferIntroductoryAPR_CAC2: Optional[float]
    BalanceTransferIntroductoryPeriod_CAC2: Optional[int]
    PromotionalOfferUsageFee: Optional[float]
    PromotionalOfferMinimumFee: Optional[float]
    PromotionalOfferMaximumFee: Optional[float]
    BalanceTransferIntroductoryFeePeriod_CAC: Optional[int]
    CashAdvanceIntroductoryAPR_CAC: Optional[float]
    CashAdvanceIntroductoryPeriod_CAC: Optional[int]
    Tier2CashAdvanceIntroductoryAPR_CAC: Optional[float]
    Tier2CashAdvanceIntroductoryAPRPeriod_CAC: Optional[int]
    CashAdvanceIntroductoryUsageFee_CAC: Optional[float]
    CashAdvanceIntroductoryMinimumFee_CAC: Optional[float]
    CashAdvanceIntroductoryMaximumFee_CAC: Optional[float]
    CashAdvanceIntroductoryFeePeriod_CAC: Optional[int]
    PurchaseIntroductoryAPR_CAC: Optional[float]
    Tier2PurchaseIntroductoryPeriod_CAC: Optional[int]
    PurchaseIntroductoryUsageFee_CAC: Optional[float]
    PurchaseIntroductoryMinimumFee_CAC: Optional[float]
    PurchaseIntroductoryMaximumFee_CAC: Optional[float]
    PurchaseIntroductoryFeePeriod_CAC: Optional[int]
    BalanceTransferRegularAPR_CAC: Optional[float]
    BalanceTransferRateType_CAC: Optional[str]
    BalanceTransferUsageFee_CAC: Optional[float]
    BalanceTransferMinimumFee_CAC: Optional[float]
    BalanceTransferMaximumFee_CAC: Optional[float]
    CashAdvanceRegularAPR_CAC: Optional[float]
    CashAdvanceRateType_CAC: Optional[str]
    CashAdvanceUsageFee_CAC: Optional[float]
    CashAdvanceMinimumFee_CAC: Optional[float]
    CashAdvanceMaximumFee_CAC: Optional[float]
    PurchaseRegularAPR_CAC: Optional[float]
    PurchaseRateType_CAC: Optional[str]
    Tier2PurchaseIntroductoryAPR_CAC: Optional[float]
    Tier2BalanceTransferIntroductoryUsageFee: Optional[float]    # Cscan_4546
    Tier2BalanceTransferIntroductoryMinimumFee:Optional[float]   # Cscan_4546
    Tier3CashAdvanceRegularAPR_CAC:Optional[float]
    @classmethod
    async def from_csv2_product_data(cls, csv2_product_data:pd.DataFrame,master_lookup_conn):
        # print(csv2_product_data['BalanceTransferIntroductoryPeriod_CAC1'].unique()[0])
        credit_data = {
                'PromotionalOffer': None,
                'PromotionalOfferAPR': None,
                'BalanceTransferIntroductoryPeriod_CAC1': None,
                'BalanceTransferIntroductoryAPRDetail_CAC':None,
                'BalanceTransferIntroductoryAPR_CAC2': None,
                'BalanceTransferIntroductoryPeriod_CAC2': None,
                'PromotionalOfferUsageFee': None,
                'PromotionalOfferMinimumFee': None,
                'PromotionalOfferMaximumFee': None,
                'BalanceTransferIntroductoryFeePeriod_CAC': None,
                'CashAdvanceIntroductoryAPR_CAC': None,
                'CashAdvanceIntroductoryPeriod_CAC': None,
                'Tier2CashAdvanceIntroductoryAPR_CAC': None,
                'Tier2CashAdvanceIntroductoryAPRPeriod_CAC': None,
                'CashAdvanceIntroductoryUsageFee_CAC': None,
                'CashAdvanceIntroductoryMinimumFee_CAC': None,
                'CashAdvanceIntroductoryMaximumFee_CAC': None,
                'CashAdvanceIntroductoryFeePeriod_CAC': None,
                'PurchaseIntroductoryAPR_CAC': None,
                'Tier2PurchaseIntroductoryPeriod_CAC': None,
                'PurchaseIntroductoryUsageFee_CAC': None,
                'PurchaseIntroductoryMinimumFee_CAC': None,
                'PurchaseIntroductoryMaximumFee_CAC': None,
                'PurchaseIntroductoryFeePeriod_CAC': None,
                'BalanceTransferRegularAPR_CAC': None,
                'BalanceTransferRateType_CAC': None,
                'BalanceTransferUsageFee_CAC': None,
                'BalanceTransferMinimumFee_CAC': None,
                'BalanceTransferMaximumFee_CAC': None,
                'CashAdvanceRegularAPR_CAC': None,
                'CashAdvanceRateType_CAC': None,
                'CashAdvanceUsageFee_CAC': None,
                'CashAdvanceMinimumFee_CAC': None,
                'CashAdvanceMaximumFee_CAC': None,
                'PurchaseRegularAPR_CAC': None,
                'PurchaseRateType_CAC': None,
                'Tier2PurchaseIntroductoryAPR_CAC': None,
                'Tier2BalanceTransferIntroductoryUsageFee': None,
                'Tier2BalanceTransferIntroductoryMinimumFee': None,
                'Tier3CashAdvanceRegularAPR_CAC':None,
            }
        if 'credit' in csv2_product_data and csv2_product_data.at[0, 'credit'] != 0:
            model=Model()
        
            purchase_rate=await model.getrateType(csv2_product_data['PurchaseRateType_CAC'].unique()[0],master_lookup_conn)
            purchaseRateType = purchase_rate.get('RateTypeName') if purchase_rate and 'RateTypeName' in purchase_rate else ''

            balanceRate=await model.getrateType(csv2_product_data['BalanceTransferRateType_CAC'].unique()[0],master_lookup_conn)
            balanceRateType = balanceRate.get('RateTypeName') if balanceRate and 'RateTypeName' in balanceRate else ''

            cashRate=await model.getrateType(csv2_product_data['CashAdvanceRateType_CAC'].unique()[0],master_lookup_conn)
            cashRateType = cashRate.get('RateTypeName') if cashRate and 'RateTypeName' in cashRate else ''
           
            credit_data.update({
            'PromotionalOffer':'No' if csv2_product_data['PromotionalOffer'].unique()[0] == 0 else 'Yes',
            'PromotionalOfferAPR':csv2_product_data['PromotionalOfferAPR'].unique()[0],
            
            'BalanceTransferIntroductoryPeriod_CAC1':csv2_product_data['BalanceTransferIntroductoryPeriod_CAC1'].unique()[0],
            'BalanceTransferIntroductoryAPRDetail_CAC':csv2_product_data['BalanceTransferIntroductoryAPRDetail_CAC'].unique()[0],
            'BalanceTransferIntroductoryAPR_CAC2':csv2_product_data['BalanceTransferIntroductoryAPR_CAC2'].unique()[0],
            'BalanceTransferIntroductoryPeriod_CAC2':csv2_product_data['BalanceTransferIntroductoryPeriod_CAC2'].unique()[0],
            'PromotionalOfferUsageFee':csv2_product_data['PromotionalOfferUsageFee'].unique()[0],
            'PromotionalOfferMinimumFee':csv2_product_data['PromotionalOfferMinimumFee'].unique()[0],
            'PromotionalOfferMaximumFee':csv2_product_data['PromotionalOfferMaximumFee'].unique()[0],
            'BalanceTransferIntroductoryFeePeriod_CAC':csv2_product_data['BalanceTransferIntroductoryFeePeriod_CAC'].unique()[0],
            'CashAdvanceIntroductoryAPR_CAC':csv2_product_data['CashAdvanceIntroductoryAPR_CAC'].unique()[0],
            'CashAdvanceIntroductoryPeriod_CAC':csv2_product_data['CashAdvanceIntroductoryPeriod_CAC'].unique()[0],
            'Tier2CashAdvanceIntroductoryAPR_CAC':csv2_product_data['Tier2CashAdvanceIntroductoryAPR_CAC'].unique()[0],
            'Tier2CashAdvanceIntroductoryAPRPeriod_CAC':csv2_product_data['Tier2CashAdvanceIntroductoryAPRPeriod_CAC'].unique()[0],
            'CashAdvanceIntroductoryUsageFee_CAC':csv2_product_data['CashAdvanceIntroductoryUsageFee_CAC'].unique()[0],
            'CashAdvanceIntroductoryMinimumFee_CAC':csv2_product_data['CashAdvanceIntroductoryMinimumFee_CAC'].unique()[0],
            'CashAdvanceIntroductoryMaximumFee_CAC':csv2_product_data['CashAdvanceIntroductoryMaximumFee_CAC'].unique()[0],
            'PurchaseIntroductoryFeePeriod_CAC':csv2_product_data['PurchaseIntroductoryFeePeriod_CAC'].unique()[0],
            'CashAdvanceIntroductoryFeePeriod_CAC':csv2_product_data['CashAdvanceIntroductoryFeePeriod_CAC'].unique()[0],
            'PurchaseIntroductoryAPR_CAC':csv2_product_data['PurchaseIntroductoryAPR_CAC'].unique()[0],
            'Tier2PurchaseIntroductoryPeriod_CAC':csv2_product_data['Tier2PurchaseIntroductoryPeriod_CAC'].unique()[0],
            'PurchaseIntroductoryUsageFee_CAC':csv2_product_data['PurchaseIntroductoryUsageFee_CAC'].unique()[0],
            'PurchaseIntroductoryMinimumFee_CAC':csv2_product_data['PurchaseIntroductoryMinimumFee_CAC'].unique()[0],
            'PurchaseIntroductoryMaximumFee_CAC':csv2_product_data['PurchaseIntroductoryMaximumFee_CAC'].unique()[0],
            'BalanceTransferRegularAPR_CAC':csv2_product_data['BalanceTransferRegularAPR_CAC'].unique()[0],
            'BalanceTransferRateType_CAC':balanceRateType,
            'BalanceTransferUsageFee_CAC':csv2_product_data['BalanceTransferUsageFee_CAC'].unique()[0],
            
            'BalanceTransferMinimumFee_CAC':csv2_product_data['BalanceTransferMinimumFee_CAC'].unique()[0],
            'BalanceTransferMaximumFee_CAC':csv2_product_data['BalanceTransferMaximumFee_CAC'].unique()[0],
            'CashAdvanceRegularAPR_CAC':csv2_product_data['CashAdvanceRegularAPR_CAC'].unique()[0],
            'CashAdvanceRateType_CAC':cashRateType,

            'CashAdvanceUsageFee_CAC':csv2_product_data['CashAdvanceUsageFee_CAC'].unique()[0],
            'CashAdvanceMinimumFee_CAC':csv2_product_data['CashAdvanceMinimumFee_CAC'].unique()[0],
            'CashAdvanceMaximumFee_CAC':csv2_product_data['CashAdvanceMaximumFee_CAC'].unique()[0],
            'PurchaseRegularAPR_CAC':csv2_product_data['PurchaseRegularAPR_CAC'].unique()[0],
            'PurchaseRateType_CAC':purchaseRateType,
            'Tier2PurchaseIntroductoryAPR_CAC':csv2_product_data['Tier2PurchaseIntroductoryAPR_CAC'].unique()[0],
            
            'Tier2BalanceTransferIntroductoryUsageFee':csv2_product_data['Tier2BalanceTransferIntroductoryUsageFee'].unique()[0],
            'Tier2BalanceTransferIntroductoryMinimumFee':csv2_product_data['Tier2BalanceTransferIntroductoryMinimumFee'].unique()[0],
            'Tier3CashAdvanceRegularAPR_CAC':csv2_product_data['Tier3CashAdvanceRegularAPR_CAC'].unique()[0],
        })  
           
        

        return cls(**credit_data)      