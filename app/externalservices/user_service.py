import requests
from fastapi import Request, HTTPException
from utils.Logger import Logger
import requests
import json
import os



class UserService():

    def __init__(self):
        self.logger = Logger(self.__class__.__name__).get()

    def __build_url_params(self,data):
        """
        Internal helper to construct a URL query string from a data dictionary.

        - Supports specific parameters for user lookup, such as 'user_id_from_name' and 'user_name_from_id'.
        - Returns a query string suitable for appending to a URL.

        Args:
            data (dict): Dictionary containing query parameters. Expected keys include:
                - "param": Operation type (e.g., "user_id_from_name", "user_name_from_id")
                - "userID": User ID (optional, used with "user_name_from_id")
                - "userName": User name (optional, used with "user_id_from_name")

        Returns:
            str or None: Query string (e.g., "param=user_id_from_name&userName=john.doe") or None if input is invalid.

        Example:
            qs = self.__build_url_params({
                "param": "user_id_from_name",
                "userName": "john.doe"
            })
            # qs == "param=user_id_from_name&userName=john.doe"
        """
        if isinstance(data,dict):
            param = data.get("param",None)
            userid = data.get("userID","")
            username = data.get("userName", "")
            url = None
            if param is not None:
                if param == "user_id_from_name":
                    # this param for finding username from userid
                    url="param="+param+"&userName="+username
                elif param == "user_name_from_id":
                    url = "param=" + param + "&userID=" + userid
                else:
                    url = "param=" + param

            return url
    def check_rbac(self, user_request):
        """
        Checks if the user has the required permissions for a resource using the external user service (RBAC).

        - Collects request metadata (headers, path, method, IP, user agent, etc.) and prepares a JSON payload.
        - Reads the user service base URL from the 'CLIENT_PROFILE_URL' environment variable.
        - Sends a POST request to the '/checkResourcePermission' endpoint with the payload and Bearer token.
        - Logs request and response details for debugging.
        - On success (statusCode 200), returns the value of 'isAllowed' from the response payload.
        - On failure (statusCode 500), raises an exception.
        - On exception, logs the error and re-raises it.

        Args:
            user_request (flask.Request): The incoming Flask request object.

        Returns:
            bool or str: True/False if permission is determined, or empty string if not allowed or on error.

        Example:
            allowed = UserService().check_rbac(request)
            if allowed:
                # User has permission
            else:
                # User does not have permission
        """
        result = ''

        self.logger.info("User request rbac: "+str(user_request.headers))
        try:
            json_data = {}
            self.logger.info("User Request in Rbac"+str(user_request.headers))
            json_data['headers'] = dict(user_request.headers)
            json_data['json'] = user_request.json if "json" in dict(user_request.headers) else ''
            json_data['view_args'] = user_request.view_args
            json_data['query_string'] = user_request.query_string.decode()
            json_data['path'] = user_request.path
            json_data['method'] = user_request.method
            json_data['IP'] = user_request.remote_addr
            json_data['browser'] = user_request.user_agent.browser
            json_data['platform'] = user_request.user_agent.platform
            json_data['version'] = user_request.user_agent.version
            json_data['string'] = user_request.user_agent.string
            json_data['service_name'] = "product_service"


            self.logger.info("json data type: " + str(type(json_data)))
            json_string = json.dumps(json_data)
            base_url = os.getenv('CLIENT_PROFILE_URL')
            exist = ''
            self.logger.info("checked upto exist blank")

            if base_url is not None and base_url:
                base_url = base_url + '/checkResourcePermission'
                self.logger.info("User service url: " + str(base_url))
                headers = {
                    'Authorization': user_request.headers['Authorization'],
                    'Content-Type': 'application/json'
                }
                response = requests.post(url=base_url, data=json_string, headers=headers)
                self.logger.info("Users response: " + str(response))
                result = json.loads(response.text)
                self.logger.info("Users response: "+str(result))
                if result['statusCode'] == 200:
                    exist = result['payload'][0]['isAllowed']
                    self.logger.info("exist available")
                    self.logger.info(exist)
                if result['statusCode'] == 500:
                    raise Exception
                self.logger.info("User Service Response: " + str(response.text))
        except Exception as e:
            self.logger.exception("Exception while calling user service: "+str(e))
            raise e
        self.logger.info("Exist data: " + str(exist))
        return exist

    def aws_auth(self, request: Request):
        """
        Validates the Bearer token from the 'Authorization' header using the external user service.

        - Reads the user service base URL from the 'CLIENT_PROFILE_URL' environment variable.
        - Checks for the presence of the 'Authorization' header in the incoming Flask request.
        - Sends a GET request to the '/validate-token' endpoint of the user service with the Bearer token.
        - Logs request and response details for debugging.
        - On success (HTTP 200), returns: {'message': True, 'code': 200, 'data': <response_json>}
        - On failure, returns: {'message': False, 'code': <status_code>, 'data': None, 'error': <error_message>}
        - On exception, returns: {'message': False, 'code': 500, 'data': None, 'error': <exception_message>}

        Returns:
            dict: Result of the authentication check.

        Example:
            result = UserService().aws_auth()
            if result.get('message') is True and result.get('code') == 200:
                # Authenticated
            else:
                # Not authenticated
        """
        result = ''
        try:
            base_url = os.getenv('CLIENT_PROFILE_URL')
            if base_url is not None and base_url:
                uri = base_url + '/validate-token'
                self.logger.info("User service URL: " + str(uri))
                # Use the request parameter, not the global Request
                auth_header = request.headers.get('authorization') or request.headers.get('Authorization')
                self.logger.info("Auth header: " + str(auth_header))
                
                # Check if Authorization header exists
                if not auth_header:
                    self.logger.error("No Authorization header found in request")
                    return {'message': False, 'code': 401, 'data': None, 'error': 'No Authorization header provided'}
                
                headers = {
                    'Authorization': auth_header
                }
                
                self.logger.info(f"Making auth request to: {uri}")
                self.logger.info(f"With headers: {headers}")
                
                response = requests.get(url=uri, headers=headers)
                self.logger.info("Auth response status: " + str(response.status_code))
                self.logger.info("Auth response body: " + str(response.text))
                
                if response.status_code == 200:
                    result = json.loads(response.text)
                    self.logger.info("User service response: " + str(result))
                    return {'message': True, 'code': 200, 'data': result}
                else:
                    self.logger.info("Auth failed with status: " + str(response.status_code))
                    return {'message': False, 'code': response.status_code, 'data': None, 'error': f'Authentication failed with status {response.status_code}'}
        except Exception as e:
            self.logger.exception("Exception while calling User service: ")
            return {'message': False, 'code': 500, 'data': None, 'error': str(e)}


# class UserService():

#     def __init__(self):
#         self.logger = Logger(self.__class__.__name__).get()

#     # def __build_url_params(self,data):
#     #     if isinstance(data,dict):
#     #         param = data.get("param",None)
#     #         userid = data.get("userID","")
#     #         username = data.get("userName", "")
#     #         url = None
#     #         if param is not None:
#     #             if param == "user_id_from_name":
#     #                 # this param for finding username from userid
#     #                 url="param="+param+"&userName="+username
#     #             elif param == "user_name_from_id":
#     #                 url = "param=" + param + "&userID=" + userid
#     #             else:
#     #                 url = "param=" + param

#     #         return url
#     async def check_rbac(self, user_request: Request):
#         """
#         Checks RBAC (Role-Based Access Control) for a given user request.

#         Parameters:
#         - user_request: The FastAPI request object

#         Returns:
#         - A boolean indicating whether the user is allowed access
#         """
#         result = ''
#         exist = ''

#         try:
#             # Logging user request headers
#             self.logger.info("User request rbac: " + str(user_request.headers))

#             # Prepare JSON data from user request
#             json_data = {
#                 'headers': dict(user_request.headers),
#                 'json': await user_request.json() if "json" in dict(user_request.headers) else '',
#                 'view_args': user_request.path_params,
#                 'query_string': user_request.query_params._dict,
#                 'path': user_request.url.path,
#                 'method': user_request.method,
#                 'IP': user_request.client.host,
#                 'browser': user_request.headers.get('user-agent', '').split('/')[0],
#                 'platform': user_request.headers.get('user-agent', '').split('/')[1],
#                 'version': user_request.headers.get('user-agent', '').split('/')[2],
#                 'string': user_request.headers.get('user-agent', ''),
#                 'service_name': "product_service"
#             }
#             self.logger.info("json data type: " + str(type(json_data)))

#             # Convert JSON data to a string
#             json_string = json.dumps(json_data)

#             # Fetch base URL from environment variable
#             base_url = os.getenv('CLIENT_PROFILE_URL')

#             if base_url:
#                 base_url = f"{base_url}/checkResourcePermission"
#                 self.logger.info("User service url: " + str(base_url))

#                 # Prepare headers for the request
#                 headers = {
#                     'Authorization': user_request.headers['Authorization'],
#                     'Content-Type': 'application/json'
#                 }

#                 # Make a POST request to the user service
#                 response = requests.post(url=base_url, data=json_string, headers=headers)
#                 self.logger.info("Users response: " + str(response))

#                 # Parse the response JSON
#                 result = response.json()
#                 self.logger.info("Users response: " + str(result))

#                 # Check if the request was successful
#                 if result['statusCode'] == 200:
#                     exist = result['payload'][0]['isAllowed']
#                     self.logger.info("exist available")
#                     self.logger.info(exist)

#                 # Check for server error
#                 if result['statusCode'] == 500:
#                     raise HTTPException(status_code=500, detail="Internal server error")

#                 self.logger.info("User Service Response: " + str(response.text))

#         except Exception as e:
#             # Log exceptions for debugging
#             self.logger.exception("Exception while calling user service: " + str(e))
#             raise HTTPException(status_code=500, detail="Error while processing user request")

#         self.logger.info("Exist data: " + str(exist))
#         return exist
        
#     # def check_rbac(self, user_request):
#     #     result = ''

#     #     self.logger.info("User request rbac: "+str(user_request.headers))
#     #     try:
#     #         json_data = {}
#     #         self.logger.info("User Request in Rbac"+str(user_request.headers))
#     #         json_data['headers'] = dict(user_request.headers)
#     #         json_data['json'] = user_request.json if "json" in dict(user_request.headers) else ''
#     #         json_data['view_args'] = user_request.view_args
#     #         json_data['query_string'] = user_request.query_string.decode()
#     #         json_data['path'] = user_request.path
#     #         json_data['method'] = user_request.method
#     #         json_data['IP'] = user_request.remote_addr
#     #         json_data['browser'] = user_request.user_agent.browser
#     #         json_data['platform'] = user_request.user_agent.platform
#     #         json_data['version'] = user_request.user_agent.version
#     #         json_data['string'] = user_request.user_agent.string
#     #         json_data['service_name'] = "product_service"


#     #         self.logger.info("json data type: " + str(type(json_data)))
#     #         json_string = json.dumps(json_data)
#     #         base_url = os.getenv('CLIENT_PROFILE_URL')
#     #         exist = ''
#     #         self.logger.info("checked upto exist blank")

#     #         if base_url is not None and base_url:
#     #             base_url = base_url + '/checkResourcePermission'
#     #             self.logger.info("User service url: " + str(base_url))
#     #             headers = {
#     #                 'Authorization': user_request.headers['Authorization'],
#     #                 'Content-Type': 'application/json'
#     #             }
#     #             response = requests.post(url=base_url, data=json_string, headers=headers)
#     #             self.logger.info("Users response: " + str(response))
#     #             result = json.loads(response.text)
#     #             self.logger.info("Users response: "+str(result))
#     #             if result['statusCode'] == 200:
#     #                 exist = result['payload'][0]['isAllowed']
#     #                 self.logger.info("exist available")
#     #                 self.logger.info(exist)
#     #             if result['statusCode'] == 500:
#     #                 raise Exception
#     #             self.logger.info("User Service Response: " + str(response.text))
#     #     except Exception as e:
#     #         self.logger.exception("Exception while calling user service: "+str(e))
#     #         raise e
#     #     self.logger.info("Exist data: " + str(exist))
#     #     return exist

#     async def aws_auth(self, request: Request):
#         """
#         Authenticates the request using the AWS user service.

#         Parameters:
#         - request: The FastAPI request object containing headers

#         Returns:
#         - A dictionary with the authentication result or an empty string if the authentication fails
#         """
#         result = ''
#         try:
#             base_url = os.getenv('CLIENT_PROFILE_URL')
#             if base_url:
#                 uri = f"{base_url}//validate-token"
#                 self.logger.info("User service URL: " + str(uri))

#                 # Extract the Authorization header from the request
#                 headers = {
#                     'Authorization': request.headers.get('Authorization')
#                 }

#                 # Make a GET request to the user service
#                 response = requests.get(url=uri, headers=headers)
                
#                 # Check the response status code
#                 if response.status_code == 200:
#                     result = response.json()
                
#                 self.logger.info("User service response: " + str(response.text))
#             else:
#                 self.logger.warning("CLIENT_PROFILE_URL environment variable is not set.")
#         except Exception as e:
#             self.logger.exception("Exception while calling User service: ")
#             raise HTTPException(status_code=500, detail="Error during authentication")

#         return result

#     # def get_user_data(self,data):
#     #     """
#     #     @desc:This function call Endpoint userdetail for getting user related data
#     #     @param:void
#     #     :return <JSON>:
#     #     """
#     #     try:
#     #         base_url = os.getenv('CLIENT_PROFILE_URL')+"/userdetails"
#     #         result = None
#     #         query_string = self.__build_url_params(data)
#     #         base_url = base_url+"?"+query_string
#     #         print(base_url)
#     #         if base_url is not None and base_url:
#     #             self.logger.info("User service URL: " + str(base_url))
#     #             headers = {
#     #                 'Authorization': Request.headers['Authorization']
#     #             }
#     #             response = requests.get(url=base_url, headers=headers)
#     #             self.logger.info("Response status"+str(response.status_code))
#     #             if response.status_code == 200:
#     #                 result = json.loads(response.text)
#     #     except Exception as e:
#     #         self.logger.exception("Exception while calling User service: ")
#     #         raise e
#     #     return result
