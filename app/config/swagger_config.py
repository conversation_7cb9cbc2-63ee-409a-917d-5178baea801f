import yaml

def load_openapi_config():
    swagger_file_path = "static/swagger.yml"
    with open(swagger_file_path, "r") as stream:
        try:
            openapi_config = yaml.safe_load(stream)
        except yaml.YAMLError as exc:
            print(exc)
            openapi_config = {}  # Handle the error accordingly
    return openapi_config

# Custom OpenAPI schema generation
def custom_openapi():
    openapi_config = load_openapi_config()

    def generate_openapi_schema():
        if 'openapi' in openapi_config:
            return openapi_config
        return {
            "openapi": "3.0.0",
            "info": {
                "title": "CSV2_Product_Detail_API",
                "description": "APIs to fetch product related Details.",
                "version": "1.0.0",
            },
            "paths": {},
            "components": {}
        }
    
    return generate_openapi_schema
