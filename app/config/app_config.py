import os
from fastapi import <PERSON><PERSON><PERSON>, Header
from fastapi.middleware.cors import CORSMiddleware
from config.swagger_config import custom_openapi
from routing.routing import routing
from middlewares.beforeRequest.beforemiddleware import BeforeRequestMiddleware
from utils.MyJSONEncoder import <PERSON><PERSON><PERSON><PERSON>nco<PERSON>


def create_app():
    app = FastAPI()
    # Enable CORS (Cross-Origin Resource Sharing) for all origins
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    # Set the custom JSON encoder
    app.json_encoder = MyJSONEncoder
    # Create Swagger config
    app.openapi = custom_openapi()

    # Register routes from routing.py
    routing(app)


    return app


