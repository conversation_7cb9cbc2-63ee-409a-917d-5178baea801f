from db.db import *
from utils.Logger import Logger
from constant.constant import FETCH_ALL



''' 
    The goal of this class is to provide methods for building and executing SQL queries dynamically based on the provided parameters. 
    This can be useful in cases where the exact structure of the query is not known in advance and can vary depending on the input.  
'''
class Querys:
    def __init__(self):
        self.logger = Logger(self.__class__.__name__).get()
        self.__db = Db()
    
    async def join_tables(self, fields_list: list, table_names: list, join_conditions: list,curs,column_name,param):
        try:
            # Ensure the number of tables and join conditions match
            if len(table_names) - 1 != len(join_conditions):
                raise ValueError("Mismatch between the number of tables and join conditions")

            # Build the initial part of the query
            query = f"SELECT {', '.join(fields_list)} FROM {table_names[0]}"

            # Add join conditions
            for i in range(len(join_conditions)):
                query += f" LEFT JOIN {table_names[i + 1]} ON {join_conditions[i]}"

            query += f" WHERE {column_name} = %s"
            self.logger.info(f"query: {query}")
            # Execute the query
            result = await self.__db.query(query=query, conn=curs, fetch_all=FETCH_ALL, params=param)
            return result

        except Exception as e:
            self.logger.exception(e)
            raise HTTPException(status_code=500, detail="Something Went Wrong: " + str(e))    
    

    async def get_sql_with_IN(self,fields_list:list, table_name: str, column_name1: str, column_name2: list, curs):
        try:
            if column_name2[0] is not None:  # Check if column_name2 is not empty and has more than one item
                placeholders = ', '.join(['%s'] * len(column_name2))
                fields_str = ', '.join(fields_list)
                query = f"SELECT {fields_str} FROM {table_name} WHERE {column_name1} IN ({placeholders})"
                params = column_name2
                result = await self.__db.query(query=query, conn=curs, fetch_all=FETCH_ALL, params=params)
            else:
                return None  # Return None if column_name2 is empty

           
            return result  # Return the list as is
        except Exception as e:
            self.logger.exception(e)
            raise HTTPException(status_code=500, detail="Something Went Wrong: " + str(e))    

    async def get_sql_table_records(self,fields_list:list, table_name: str, column_name1: str, column_name2, curs):
        try:
            if column_name2:  # Check if column_name2 is not empty and has more than one item
                # Create a comma-separated string of field names
                fields_str = ', '.join(fields_list)
                query = f"SELECT {fields_str} FROM {table_name} WHERE {column_name1} = %s"
                params = column_name2
                # print("------------------",query,params)
                result = await self.__db.query(query=query, conn=curs, fetch_all=FETCH_ALL, params=params)
            else:
                return None  # Return None if column_name2 is empty

            
            if len(result) == 1:
                return result[0]  # Return the single item as a string
            else:
                return result  # Return the list as is 
        except Exception as e:
            self.logger.exception(e)
            raise HTTPException(status_code=500, detail="Something Went Wrong: " + str(e))      

    async def get_sql_table_records_reward(self, fields_list: list, table_name: str, column_name1: str, column_name2, curs):
        try:
            if column_name2:  
                fields_str = ', '.join(fields_list)
                query = f"SELECT {fields_str} FROM {table_name} WHERE {column_name1} IN ({column_name2})"
                result = await self.__db.query(query=query, conn=curs, fetch_all=FETCH_ALL)
            else:
                return None  
            if len(result) == 1:
                return result[0]  
            else:
                return result  

        except Exception as e:
            self.logger.exception(e)
            raise HTTPException(status_code=500, detail="Something Went Wrong: " + str(e))

    async def join_tables_with_IN(self, fields_list: list, table_names: list, join_conditions: list,curs,column_name,param):
        try:
            # Ensure the number of tables and join conditions match
            if len(table_names) - 1 != len(join_conditions):
                raise ValueError("Mismatch between the number of tables and join conditions")

            # Build the initial part of the query
            query = f"SELECT {', '.join(fields_list)} FROM {table_names[0]}"

            # Add join conditions
            for i in range(len(join_conditions)):
                query += f" LEFT JOIN {table_names[i + 1]} ON {join_conditions[i]}"

            placeholders = ', '.join(['%s'] * len(param))
            
            query += f" WHERE {column_name} IN ({placeholders})"
            # Execute the query
            result = await self.__db.query(query=query, conn=curs, fetch_all=FETCH_ALL, params=param)
            
            return result

        except Exception as e:
            self.logger.exception(e)
            raise HTTPException(status_code=500, detail="Something Went Wrong: " + str(e))                           

    async def join_tables_distinct(self, fields_list: list, table_names: list, join_conditions: list,curs,column_name,param,sort_by: str = None):
        try:
            # Ensure the number of tables and join conditions match
            if len(table_names) - 1 != len(join_conditions):
                raise ValueError("Mismatch between the number of tables and join conditions")

            # Build the initial part of the query
            query = f"SELECT DISTINCT {', '.join(fields_list)} FROM {table_names[0]}"

            # Add join conditions
            for i in range(len(join_conditions)):
                query += f" JOIN {table_names[i + 1]} ON {join_conditions[i]}"

            query += f" WHERE {column_name} = %s"
            if sort_by:
                query += f" ORDER BY {sort_by} ASC"
            # print(query)
            # Execute the query
            result = await self.__db.query(query=query, conn=curs, fetch_all=FETCH_ALL, params=param)
            
            return result
        except Exception as e:
            self.logger.exception(e)
            raise HTTPException(status_code=500, detail="Something Went Wrong: " + str(e))  

    async def fetch_affinity_ids(self, pid, db1,sort_by: str = None):
        try:
            query_affinity_id = f"SELECT affinityID FROM cscan_panelist_affinity WHERE panelist_id = {pid}"
            result_affinity_id = await self.__db.query(query=query_affinity_id, conn=db1, fetch_all=True)
            return result_affinity_id
        except Exception as e:
            self.logger.exception(e)
            raise HTTPException(status_code=500, detail="Something Went Wrong: " + str(e))

    async def fetchAffinNames(self, affins,master_lookup_conn):
        affins = ','.join(map(str, affins))
        try:
            query_affinity = f"SELECT DISTINCT affinityName FROM cscan_affinity WHERE affinityID IN ({affins}) ORDER BY affinityName"
            result_affinity = await self.__db.query(query=query_affinity, conn=master_lookup_conn, fetch_all=True)
            # print("result_affinity",result_affinity,query_affinity)
            return result_affinity
        except Exception as e:
            self.logger.exception(e)
            raise HTTPException(status_code=500, detail="Something Went Wrong: " + str(e))

    async def fetch_com_ids(self, pid, db1,sort_by: str = None):
        try:
            query_com_id = f"SELECT companyID FROM cscan_panelist_company WHERE panelist_id = {pid}"
            result_com_id = await self.__db.query(query=query_com_id, conn=db1, fetch_all=True)
            return result_com_id
        except Exception as e:
            self.logger.exception(e)
            raise HTTPException(status_code=500, detail="Something Went Wrong: " + str(e))

    async def fetchComNames(self, affins,master_lookup_conn):
        affins = ','.join(map(str, affins))
        try:
            query_affinity = f"SELECT DISTINCT companyName FROM cscan_company WHERE companyID IN ({affins}) ORDER BY companyName"
            result_affinity = await self.__db.query(query=query_affinity, conn=master_lookup_conn, fetch_all=True)
            # print("result_affinity",result_affinity,query_affinity)
            return result_affinity
        except Exception as e:
            self.logger.exception(e)
            raise HTTPException(status_code=500, detail="Something Went Wrong: " + str(e))
        

    async def fetch_panelist_mover(self, pid, db1,sort_by: str = None):
        try:
            query_affinity_id = f"SELECT pm_date,stateID1,stateID2,postalcode1,postalcode2 FROM cscan_panelists_mover WHERE panelist_id={pid} ORDER BY pm_date ASC"
            # print("query_affinity_id",query_affinity_id)
            result_affinity_id = await self.__db.query(query=query_affinity_id, conn=db1, fetch_all=True)
            return result_affinity_id
        except Exception as e:
            self.logger.exception(e)
            raise HTTPException(status_code=500, detail="Something Went Wrong: " + str(e))

    async def states(self, db1,sort_by: str = None):
        try:
            query = f"SELECT stateID, stateName FROM cscan_state"
            states = await self.__db.query(query=query, conn=db1, fetch_all=True)
            return states
        except Exception as e:
            self.logger.exception(e)
            raise HTTPException(status_code=500, detail="Something Went Wrong: " + str(e))
        

    async def updateemailtrack(self,tracking_id,product_conn):
        update_tracking_sql = """
            UPDATE cscan_email_track 
            SET is_opened = 1, is_clicked = 1 
            WHERE id = %s
        """
        async with product_conn.cursor() as cursor:
            await cursor.execute(update_tracking_sql, (tracking_id,))
            rows = cursor.rowcount
            await product_conn.commit()
            if rows == 0:
                return False
            return True
