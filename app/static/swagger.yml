openapi: "3.0.0"
info:
  title: CSV2_Product_Detail_API
  description: APIs to fetch product related Details.
  version: 1.0.0
paths:
  /data/productdata/:
    post:
      tags:
        - Products
      summary: Retrieve a product by ID
      requestBody:
        description: Request payload containing the product ID
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                product_id:
                  type: integer
                  example: 8467592
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  product_id:
                    type: integer
                    example: 8467592
        '404':
          description: Product not found

