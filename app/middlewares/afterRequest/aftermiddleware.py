from fastapi import <PERSON><PERSON><PERSON>, Request, Response

class AfterRequestMiddleware:
    def __init__(self, app: FastAPI):
        self.app = app

    async def __call__(self, request: Request, call_next):
        response: Response = await call_next(request)
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Methods'] = 'GET,PUT,POST,DELETE,PATCH,OPTIONS'
        response.headers['Access-Control-Allow-Headers'] = '*'
        return response
