from os import abort
from fastapi import Request, Depends, HTTPException, Security
from utils.Logger import Logger
from externalservices.user_service import UserService
from functools import wraps

# Simple dependency function for authentication
async def auth_dependency(request: Request):
    Logger(f"Auth headers: {request.headers.get('Authorization', 'No Auth header')}")
    if not request.headers.get('Authorization'):
        <PERSON><PERSON>("Auth failed: No Authorization header found")
        raise HTTPException(status_code=400, detail="No Authorization header found")
    result = UserService().aws_auth(request)
    Logger(f"Auth result: {result}")
    if not result['message']==True and result['code']!=200:
        Logger("Auth failed: Authentication failed")
        raise HTTPException(status_code=401, detail="Authentication failed")
    
    return True

# Simple dependency function for permission checking
async def permission_dependency(request: Request):
    from utils.Logger import Logger
    from externalservices.user_service import UserService
    exist = UserService().check_rbac(request)
    if not exist:
        raise HTTPException(status_code=403, detail="Permission denied")
    return True

def authentication_required():
    return auth_dependency

def permission_required():
    return permission_dependency



# from functools import wraps
# import flask
# from flask import request, abort
# from externalservices.user_service import UserService
# from flask_restful import abort
# from utils.Logger import Logger


# def permission_required():
#     def decorator(f):
#         @wraps(f)
#         def decorated_function(*args, **kwargs):
#             try:
#                 exist = UserService().check_rbac(request)
#             except Exception as e:
#                 abort(500)
#             if not exist:
#                 abort(403)
#             return f(*args, **kwargs)

#         return decorated_function

#     return decorator


# def authentication_required():
#     def decorator(f):
#         @wraps(f)
#         def decorated_function(*args, **kwargs):
#             logger = Logger('decorators').get()
#             try:
#                 result = UserService().aws_auth()
#             except Exception as e:
#                 e.with_traceback()
#                 logger.info(str(e))
#                 abort(500)
#             if not result['is_valid']:
#                 flask.abort(401, result['message'])
#             return f(*args, **kwargs)

#         return decorated_function

#     return decorator