from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request
from starlette.responses import Response
from utils.Logger import Logger

class BeforeRequestMiddleware(BaseHTTPMiddleware):
    def __init__(self, app):
        super().__init__(app)
        self.logger = Logger(self.__class__.__name__).get()
        
    async def dispatch(self, request: Request, call_next):
        # Add your pre-request logic here
        self.logger.info("Before request logic executed")
        
        # Continue with the request
        response = await call_next(request)
        
        # Optionally add post-request logic here
        self.logger.info("After request logic executed")
        
        return response



# import os
# from werkzeug.wrappers import Request, Response, ResponseStream
# from pprint import pprint


# class BeforeRequestMiddleware():

#     def __init__(self, app):
#         self.app = app

#     def __call__(self, environ, start_response):
#         return self.app(environ, start_response)
