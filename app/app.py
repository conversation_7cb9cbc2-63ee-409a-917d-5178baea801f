from db.db import *
from config.app_config import create_app
from middlewares.afterRequest.aftermiddleware import AfterRequestMiddleware
from dotenv import load_dotenv
from fastapi.staticfiles import StaticFiles
import uvicorn


load_dotenv()
app = create_app()
app.mount("/static", StaticFiles(directory="static"), name="static")
# Add the middleware to the app
middleware_instance = AfterRequestMiddleware(app)
app.middleware('http')(middleware_instance)

if __name__ == "__main__":
    # uvicorn.run(app, host = os.getenv('FAST_API_HOST'), port = os.getenv('FAST_API_PORT'), reload = os.getenv('FAST_API_DEBUG'))
    uvicorn.run(app, host="0.0.0.0", port=8000)
