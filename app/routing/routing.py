import inspect
from fastapi import <PERSON><PERSON><PERSON>,Request, HTTPException
from starlette.responses import JSONResponse
from fastapi_utils.inferring_router import InferringRouter
from typing import Type,Optional
from services.rawdata.resource import ProductDetail, AffinityDetails,TrackDetails
# from services.redis.key_redis import Redis_key
from middlewares.decorators.decorators import permission_required, authentication_required
from fastapi import Depends

router=InferringRouter()
auth_dependency = [Depends(authentication_required())]
authorized= Depends(permission_required())

def get_route_map(app):
    '''
       This function returns a list of route configurations. Each route configuration includes the class to be used, URL pattern, and decorators.
    '''
    return [
        {
            'obj': ProductDetail,  # The class that handles the '/data/productdata' route
            'pattern': '/data/productdata',  # The URL pattern
            'decorators': [],  # Any decorators to apply to this route
            'method':['POST']
        },
        {
            'obj': AffinityDetails,  # New class
            'pattern': '/data/affinities',  # New URL pattern
            'decorators': [],  # Optional decorators
            'method': ['POST']  # Allowed HTTP methods
        },
        {
            'obj': TrackDetails, 
            'pattern': '/data/tracking',  
            'decorators': [], 
            'method': ['POST'] 
        }

    ]

async def custom_route_handler(request: Optional[Request], class_route: Type):
    '''
       This function handles the execution of the routes by invoking the appropriate methods on the class.
    '''
    try:
        instance = class_route()
        
        # Determine the method name based on the HTTP method
        method_name = request.method.lower()
        handler = getattr(instance, method_name, None)
        if handler and callable(handler):
            # Inspect the handler's signature
            sig = inspect.signature(handler)
            if 'json_data' in sig.parameters:
                json_data = await request.json() if request else {}
                result = await handler(json_data)
            else:
                result = await handler()
            
            return JSONResponse(content=result, status_code=200)
        else:
            raise HTTPException(status_code=405, detail=f"No handler for {request.method} method")
    except HTTPException as he:
        raise he
    except Exception as e:
        result = {'message': f"Something Went Wrong: {e}"}
        return JSONResponse(content=result, status_code=500)

# This function creates an HTTP route handler function for a given class route.
def create_handler(class_route):
    async def handler(request: Request):
        return await custom_route_handler(request, class_route)
    return handler

def routing(app: FastAPI):
    '''
       This function configures the routes and integrates them into the FastAPI app.
    '''
    route_map = get_route_map(app)
    for route in route_map:
        dependencies = route['decorators']
        class_route = route['obj']
        route_path = route['pattern']
        method=route['method']
        custom_handler = create_handler(class_route)
        router.add_api_route(
            path=route_path,
            endpoint=custom_handler,
            methods=method,  
            dependencies=dependencies,
        )
        app.include_router(router)
    return app
