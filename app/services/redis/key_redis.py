from time import time
from fastapi import  Header, HTTPException
from db.db import *


class Redis_key:
    def __init__(self):
        self.logger = Logger(self.__class__.__name__).get()
        self._db=Db()


    async def post(self, json_data: dict):
        try:
            product_id = json_data.get('product_id')  # Assuming you have a product_id in the JSON data
            key = f"product_detail{product_id}"  # Construct the key with the prefix and product_id
            delete_key=await self._db.delete_key_from_redis_cache(key)
            return delete_key
        except Exception as e:
            self.logger.exception(e)
            raise HTTPException(status_code=500, detail="Something Went Wrong: " + str(e))