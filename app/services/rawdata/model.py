import pandas as pd
import asyncio
from db.db import *
from query_functions.querys import Querys
from utils.Logger import Logger
import math
import numpy as np
# from pydantic_models.pydantic_classes import *



class Model:

    def __init__(self):
        self.logger = Logger(self.__class__.__name__).get()
        self.__db_query=Querys()

    

    async def get_masterlookup_data(self,master_loop_up_query,csv2_product_Data,master_lookup_conn):
        try: 
            rawdata = {}
            for item in master_loop_up_query:
                # print("item==",item)
                fields_list,table_name, column_name, data_column_name = item
                if data_column_name in csv2_product_Data.columns and csv2_product_Data[data_column_name] is not None:
                    data = csv2_product_Data[data_column_name].unique().tolist()
                    if len(data) > 1:
                        task = asyncio.create_task(
                            self.__db_query.get_sql_with_IN(
                                fields_list,table_name, column_name, list(data), master_lookup_conn
                            )
                        )
                    else:    
                        task = asyncio.create_task(
                            self.__db_query.get_sql_table_records(
                                fields_list,table_name, column_name, data, master_lookup_conn
                            )
                        )
                    # Execute the task
                    result = await task
                    if not result:
                        rawdata[fields_list[0]]=None
                    # Check if the length of result is greater than 
                    if  len(result) > 1:
                        # Check if result is not empty and it is a list of dictionaries
                        if result and isinstance(result, list) and isinstance(result[0], dict):
                            # Get the key from the first dictionary in result (assuming all dictionaries have the same key)
                            r_key = list(result[0].keys())[0]
                            
                            # Check if the key is present in the dictionaries within the result list
                            if all(r_key in entry for entry in result):
                                # Extract values from the specified key in each dictionary and join them with commas
                                rawdata[r_key] = ', '.join(str(entry[r_key]) for entry in result)
                    # Bug fix jira id - 6524              
                    elif len(result) == 1 and isinstance(result , list):
                        r_key = list(result[0].keys())[0]
                        # In the case of a single entry, and result type is list, then we directly assign the value to the key
                        rawdata[r_key]= result[0][r_key]
                    elif len(result) == 1 and isinstance(result , dict):
                        r_key = list(result.keys())[0]
                        # In the case of a single entry, and type of result is dict, then we directly assign the value to the key
                        rawdata[r_key]= result[r_key]
                    # Bug fix jira id - 6524 ,  end here
                    else:
                        # Handle the case when result is empty or None
                        pass
                else:
                    pass    
            return rawdata 
        except Exception as e:
            self.logger.exception(e) 
    
    async def get_print_type(self,master_lookup_conn,csv2_product_Data):
        try:
            fields_list=["cscan_publication.publicationID","cscan_publication.publicationName","cscan_print_type.print_typeName"]
            table_names=["cscan_publication","cscan_print_type"]
            #publication_id=int(csv2_product_Data['publication_id'].unique()[0])
            publication_id=list(map(int,csv2_product_Data['publication_id']))
            join_condition=["cscan_publication.print_typeID=cscan_print_type.print_typeID"]
            pub_details = []
            for id in publication_id:
                publicationdetails=await self.__db_query.join_tables(fields_list, table_names, join_condition,master_lookup_conn,"cscan_publication.publicationID",id)
                pub_details+=publicationdetails
            return pub_details 
        except Exception as e:
            # Log exceptions for debugging
            self.logger.exception(e) 
    
    async def get_affinity_category(self,master_lookup_conn,rawdata): 
        """
            Fetches affinity category data based on the provided raw data.

            Args:
                master_lookup_conn: Database connection for master lookup.
                rawdata (dict): Raw data containing information needed to fetch affinity category.

            Returns:
                list: List of affinity category data.
        """
        try:
            # Define parameters for fetching affinity category
            fields_list=["cscan_affinity_category.AffinityCategoryName","cscan_affinity.affinityName"]
            table_names=["cscan_affinity","cscan_affinity_category"]
            join_conditions=["cscan_affinity.AffinityCategoryID=cscan_affinity_category.AffinityCategoryID"]
            #data=int(rawdata['afinity_id'].unique()[0])
            data = list(map(int,rawdata['afinity_id'].unique()))
            affinitynames=[]
            for affid in data:
            # Fetch affinity category data from the database
                category=await self.__db_query.join_tables(
                            fields_list,table_names,join_conditions,master_lookup_conn,"cscan_affinity.affinityID",affid
                        )
                affinitynames+=category
                print("affinitynames",affinitynames)
            return affinitynames
           
        except Exception as e:
           
            # Log exceptions for debugging
            self.logger.exception(e) 

    async def get_panalist_eve(self,master_lookup_conn,csv2_product_Data:pd.DataFrame):
        """
            Retrieves panelist data along with ValueScore for Household based on rawdata and panelist media values.

            Args:
                master_lookup_conn: Database connection for master lookup.
                csv2_product_Data: DataFrame containing panelist data.
                rawdata: Raw data containing panelist_score information.
        """
        try:
            if 'ppmv' in csv2_product_Data.columns:
                panelist_value='ppmv'
            else:
                panelist_value='ppeve'   
               
            selected_columns = ['ppage','panelist_id',panelist_value, 'ppdate', 'fico_range_id', 'vantage_range_id', 'creditVision_range_id']
            panalist = csv2_product_Data[selected_columns].drop_duplicates(subset=['panelist_id'])
            sum_panalist_media_value = float(panalist[panelist_value].sum())
            panalist_list = []

            for _, row in panalist.iterrows():
                # Access values using column names
                panalist_ID = row['panelist_id']
                creditVision_range_id = row['creditVision_range_id']
                fico_range_id = row['fico_range_id']
                vantage_range_id = row['vantage_range_id']
                ppdate=row['ppdate']
                panelist_age=row['ppage']
                panelist_score_mapping=await self.__db_query.get_sql_table_records(["ValueScore_for_Household","RAPA_EMLC_ZIP_REL","RAHO_HOMLC_ZIP_REL"],"cscan_panelists_appends","panelist_id",panalist_ID,master_lookup_conn)
                fields_list=['cscan_panelists.competi_id','DATEDIFF(CURDATE(),cscan_panelists.birthdate) as agedays','cscan_panelists.gender',
                             'cscan_panelists.incomeID','cscan_panelists.stateID','cscan_incometype.incomeName',
                             'cscan_state.stateName']
                table_names=['cscan_panelists','cscan_incometype','cscan_state']
                join_conditions=['cscan_panelists.incomeID=cscan_incometype.incomeID',
                                 'cscan_panelists.stateID=cscan_state.stateID']
                cscan_panelist_info=await self.__db_query.join_tables(fields_list,table_names,join_conditions,master_lookup_conn,'cscan_panelists.panelist_id',panalist_ID)
                if cscan_panelist_info and len(cscan_panelist_info) > 0:
                    if 'agedays' in cscan_panelist_info[0] and cscan_panelist_info[0]['agedays'] is not None:
                        if panelist_age == None or panelist_age == 0:
                            panelist_age = math.floor(int(cscan_panelist_info[0]['agedays'])/365)
                # Fetching panalistScoreRange based on fico_range_id, creditVision_range_id, vantage_range_id
                panalistScoreRange = await self.__db_query.get_sql_with_IN(['id', 'score_min'], 'cscan_score_range', 'id', [fico_range_id, creditVision_range_id, vantage_range_id], master_lookup_conn)
                # print("panalistScoreRange",panalistScoreRange)
                fico_range, vantage_range, creditvision_range = 0, 0, 0
                if panalistScoreRange:
                    score_dataframe = pd.DataFrame(panalistScoreRange)
                    # Access specific values in the score_dataframe
                    if fico_range_id in score_dataframe['id'].values:
                        fico_range = score_dataframe.loc[score_dataframe['id'] == fico_range_id, 'score_min'].values[0]

                    if vantage_range_id in score_dataframe['id'].values:
                        vantage_range = score_dataframe.loc[score_dataframe['id'] == vantage_range_id, 'score_min'].values[0]

                    if creditVision_range_id in score_dataframe['id'].values:
                        creditvision_range = score_dataframe.loc[score_dataframe['id'] == creditVision_range_id, 'score_min'].values[0]

                    # Fetching ValueScore_for_Household based on panelist_id
                    # panelist_id_to_fetch = panalist_ID  # Replace with the desired panelist_id
                # value_score_for_household = panelist_score_mapping.get(panalist_ID, None)  
                VSfH_average,VSfH_description,valueS=0,None,None    
                if panelist_score_mapping:   
                    valueScore = await self.__db_query.get_sql_table_records(['code','description', 'VSfH_average'], 'cscan_valuescore_for_household', 'code',panelist_score_mapping['ValueScore_for_Household'], master_lookup_conn)
                    if valueScore:
                        valueS=valueScore['code']
                        VSfH_average=valueScore['VSfH_average']
                        VSfH_description=valueScore['description']
                panalist_dict = {
                    'fico_score_range': int(fico_range),
                    'vantage_score_range': int(vantage_range),
                    'creditvision_score_range': int(creditvision_range),
                    'panelist_id': panalist_ID,
                    "compti_id":cscan_panelist_info[0]['competi_id'],
                    'valuseScore':valueS,
                    'RAPA_ISO':panelist_score_mapping['RAPA_EMLC_ZIP_REL'] if panelist_score_mapping else None,
                    'RAHO_ISO':panelist_score_mapping['RAHO_HOMLC_ZIP_REL'] if panelist_score_mapping else None,
                    'VSfH_average': VSfH_average,
                    'VSfH_description': VSfH_description,
                    panelist_value: round(float(row[panelist_value])),
                    'ppdate':str(ppdate),
                    'ppage':panelist_age,
                    'pstate':cscan_panelist_info[0]['stateName'],
                    'pincome':cscan_panelist_info[0]['incomeName'],
                }
                panalist_list.append(panalist_dict)

            panalist_list.append({f'total_{panelist_value}': round(sum_panalist_media_value)})
            return panalist_list
        except Exception as e:
            # Log exceptions for debugging
            self.logger.exception(e)  

    # async def get_panalist_eve(self, master_lookup_conn, csv2_product_Data: pd.DataFrame):
    #     """
    #     Retrieves panelist data along with ValueScore for Household using batch queries
    #     (via get_sql_with_IN) and pandas merge operations to avoid per-panelist loops.

    #     Args:
    #         master_lookup_conn: Database connection for master lookup.
    #         csv2_product_Data: DataFrame containing panelist data.

    #     Returns:
    #         A list of dictionaries with panelist information plus a summary total.
    #     """
    #     try:
    #         # Determine which media value column to use.
    #         panelist_value = 'ppmv' if 'ppmv' in csv2_product_Data.columns else 'ppeve'
            
    #         # Select required columns and remove duplicate panelist entries.
    #         selected_columns = [
    #             'ppage', 'panelist_id', panelist_value, 'ppdate',
    #             'fico_range_id', 'vantage_range_id', 'creditVision_range_id'
    #         ]
    #         panalist_df = csv2_product_Data[selected_columns].drop_duplicates(subset=['panelist_id'])
            
    #         # Clean the media value column to replace non-finite values with 0.
    #         panalist_df[panelist_value] = panalist_df[panelist_value].replace({np.nan: 0, np.inf: 0, -np.inf: 0})
            
    #         total_media_value = float(panalist_df[panelist_value].sum())
    #         panelist_ids = panalist_df['panelist_id'].tolist()
            
    #         # 1. Retrieve panelist score mapping for multiple panelists.
    #         score_mapping = await self.__db_query.get_sql_with_IN(
    #             ["panelist_id", "ValueScore_for_Household", "RAPA_EMLC_ZIP_REL", "RAHO_HOMLC_ZIP_REL"],
    #             "cscan_panelists_appends",
    #             "panelist_id", panelist_ids, master_lookup_conn)
    #         score_mapping_df = pd.DataFrame(score_mapping)
            
    #         # 2. Retrieve basic panelist info from cscan_panelists.
    #         basic_info = await self.__db_query.get_sql_with_IN(
    #             ["panelist_id", "competi_id", "birthdate", "incomeID", "stateID"],
    #             "cscan_panelists",
    #             "panelist_id", panelist_ids, master_lookup_conn)
    #         basic_info_df = pd.DataFrame(basic_info)
            
    #         # Merge basic info into main DataFrame.
    #         merged = panalist_df.merge(basic_info_df, on="panelist_id", how="left")
            
    #         # Compute age from birthdate if ppage is missing or zero.
    #         missing_age = merged['ppage'].isnull() | (merged['ppage'] == 0)
    #         if missing_age.any() and 'birthdate' in merged.columns:
    #             merged.loc[missing_age, 'ppage'] = merged.loc[missing_age, 'birthdate'].apply(
    #                 lambda x: (pd.Timestamp.today() - pd.to_datetime(x)).days // 365
    #             )
            
    #         # 3. Retrieve income information for all involved incomeIDs.
    #         income_ids = list(basic_info_df['incomeID'].dropna().unique())
    #         income_info = await self.__db_query.get_sql_with_IN(
    #             ["incomeID", "incomeName"],
    #             "cscan_incometype",
    #             "incomeID", income_ids, master_lookup_conn)
    #         income_info_df = pd.DataFrame(income_info)
            
    #         # 4. Retrieve state information for all involved stateIDs.
    #         state_ids = list(basic_info_df['stateID'].dropna().unique())
    #         state_info = await self.__db_query.get_sql_with_IN(
    #             ["stateID", "stateName"],
    #             "cscan_state",
    #             "stateID", state_ids, master_lookup_conn)
    #         state_info_df = pd.DataFrame(state_info)
            
    #         # Merge income and state info into the main DataFrame.
    #         merged = merged.merge(income_info_df, on="incomeID", how="left")
    #         merged = merged.merge(state_info_df, on="stateID", how="left")
            
    #         # Merge panelist score mapping into the main DataFrame.
    #         merged = merged.merge(score_mapping_df, on="panelist_id", how="left")
            
    #         # 5. Retrieve score ranges for fico, vantage, and creditVision in one query.
    #         all_range_ids = list(set(
    #             merged['fico_range_id'].tolist() +
    #             merged['vantage_range_id'].tolist() +
    #             merged['creditVision_range_id'].tolist()
    #         ))
    #         score_range = await self.__db_query.get_sql_with_IN(
    #             ['id', 'score_min'],
    #             'cscan_score_range',
    #             'id', all_range_ids, master_lookup_conn)
    #         score_range_df = pd.DataFrame(score_range)
            
    #         # Merge score ranges into the main DataFrame for each type.
    #         merged = merged.merge(
    #             score_range_df.rename(columns={'id': 'fico_range_id', 'score_min': 'fico_score_range'}),
    #             on='fico_range_id', how='left')
    #         merged = merged.merge(
    #             score_range_df.rename(columns={'id': 'vantage_range_id', 'score_min': 'vantage_score_range'}),
    #             on='vantage_range_id', how='left')
    #         merged = merged.merge(
    #             score_range_df.rename(columns={'id': 'creditVision_range_id', 'score_min': 'creditvision_score_range'}),
    #             on='creditVision_range_id', how='left')
            
    #         # 6. Retrieve ValueScore details for multiple ValueScore_for_Household codes.
    #         value_codes = list(merged['ValueScore_for_Household'].dropna().unique())
    #         value_score_details = await self.__db_query.get_sql_with_IN(
    #             ['code', 'description', 'VSfH_average'],
    #             'cscan_valuescore_for_household',
    #             'code', value_codes, master_lookup_conn)
    #         value_score_df = pd.DataFrame(value_score_details)
            
    #         # Merge value score details into the main DataFrame.
    #         merged = merged.merge(value_score_df, left_on='ValueScore_for_Household', right_on='code', how='left', suffixes=("", "_value"))
            
    #         # 7. Prepare the final output DataFrame by selecting and renaming columns.
    #         rename_map = {
    #             'fico_score_range': 'fico_score_range',
    #             'vantage_score_range': 'vantage_score_range',
    #             'creditvision_score_range': 'creditvision_score_range',
    #             'panelist_id': 'panelist_id',
    #             'competi_id': 'compti_id',
    #             'code': 'valuseScore',
    #             'RAPA_EMLC_ZIP_REL': 'RAPA_ISO',
    #             'RAHO_HOMLC_ZIP_REL': 'RAHO_ISO',
    #             'VSfH_average': 'VSfH_average',
    #             'description': 'VSfH_description',
    #             panelist_value: panelist_value,
    #             'ppdate': 'ppdate',
    #             'ppage': 'ppage',
    #             'stateName': 'pstate',
    #             'incomeName': 'pincome'
    #         }
    #         final_df = merged[list(rename_map.keys())].rename(columns=rename_map)
    #         #self.logger.info(f"final_df:{final_df}")
            
    #         # Convert any datetime columns (e.g., 'ppdate') to string format.
    #         if 'ppdate' in final_df.columns:
    #             final_df['ppdate'] = pd.to_datetime(final_df['ppdate'], errors='coerce').dt.strftime('%Y-%m-%d')
            
    #         # Round the media value column.
    #         final_df[panelist_value] = final_df[panelist_value].round().astype(int)
            
    #         # Convert DataFrame to list of dictionaries and append the summary total.
    #         panalist_list = final_df.to_dict(orient='records')
    #         self.logger.info(f"result_list:{panalist_list}")
    #         panalist_list.append({f'total_{panelist_value}': round(total_media_value)})
            
    #         # Validate final output to ensure no non-finite floats remain.
    #         for item in panalist_list:
    #             for key, value in item.items():
    #                 if isinstance(value, float) and (np.isnan(value) or np.isinf(value)):
    #                     item[key] = 0
            
    #         return panalist_list

    #     except Exception as e:
    #         self.logger.exception(e)


    async def get_sector(self,product_id,product_conn,master_lookup_conn):
        """
            Retrieves sector information for a given product by querying related database tables,
            then processes and returns the sector data in a structured format.

            Parameters:
            - product_id: ID of the product to retrieve sector information for
            - product_conn: Database connection for product data
            - master_lookup_conn: Database connection for master lookup data

            Returns:
            - A list of dictionaries, each containing sector and its corresponding categories.
        """
        try:
            # Fetch mapping sector data for the product`
            mapping_sector_data=await self.__db_query.get_sql_table_records(["sector_id","id","sequence_no"],"cscan_product_sector_mapping","product_id",product_id,product_conn)
            if isinstance(mapping_sector_data, dict):
                mapping_sector_data = {key: [value] for key, value in mapping_sector_data.items()}
            # Convert the data to a DataFrame
            mapping_sector_dataframe=pd.DataFrame(mapping_sector_data)
            if 'sector_id' not in mapping_sector_dataframe.columns:
                self.logger.info("Column 'sector_id' not found in mapping sector data. Returning empty list.")
                return []
            sector_idsAll = list(mapping_sector_dataframe['sector_id'])
            
            # Extract unique sector IDs and corresponding IDs and sequence_nos
            mapSecdf=await self.__db_query.get_sql_with_IN(["sectorID","sectorName","parentID"],"cscan_sector","sectorID",mapping_sector_dataframe['sector_id'].unique().tolist(),master_lookup_conn)
            #sector_dataframe=pd.DataFrame(sector_names)
            sector_names = []
            for sid in range(len(sector_idsAll)):
                for ele in mapSecdf:
                    if int(ele["sectorID"]) == int(sector_idsAll[sid]):
                        sector_names.append(ele)
                        break
            
            sectorIDs = [x["sectorID"] for x in sector_names if x["parentID"] == 0]
            
            result = []
            for secid in sectorIDs:
                
                res = dict()
                for x in sector_names:
                    if x["sectorID"] == secid:
                        res["sector"] = x["sectorName"]
                        res["sector_id"] = secid
                        sector_names.remove(x)
                        break
            
                for x in sector_names:
                    if x["parentID"] == secid:
                        res["category"] = x["sectorName"]
                        catid = x["sectorID"]
                        sector_names.remove(x)
                        break
                res['sub_category']=""
                res['sub_sub_category']=""
                try:
                    for x in sector_names:
                        if x["parentID"] == catid:
                            res["sub_category"] = x["sectorName"]
                            subcatid = x["sectorID"]
                            sector_names.remove(x)
                            break
            
                    try:
                        for x in sector_names:
                            if x["parentID"] == subcatid:
                                res["sub_sub_category"] = x["sectorName"]
                                subsubcatid = x["sectorID"]
                                sector_names.remove(x)
                                break
                    except:
                        pass
                except:
                    pass
                result.append(res)

            # final_result = list({(d['sector'], d['category'], d['sub_category'], d['sub_sub_category'],d['sector_id']): d for d in result}.values())
            return result
        except Exception as e:
            # Log exceptions for debugging
            self.logger.exception(e)
            raise e 
           
        #     # Group by sector_id, order by id and sequence_no in ascending order
        #     sorted_sectors = mapping_sector_dataframe.sort_values(by=['sector_id', 'id', 'sequence_no']).groupby('sequence_no')['sector_id'].apply(lambda x: x.values.tolist()).reset_index()
        #     # print(sorted_sectors)
        #     result = []
        #     for _, row in sorted_sectors.iterrows():
        #         sector_name=sector_dataframe.loc[sector_dataframe['sectorID'] == row['sector_id'][0], 'sectorName'].values[0]
        #         # Initialize variables
        #         category = ''
        #         sub_category = ''
        #         sub_sub_category = ''
        #         if len(row['sector_id']) > 1:
        #             category = sector_dataframe.loc[sector_dataframe['sectorID'] == row['sector_id'][1], 'sectorName'].values[0]
               
        #         if len(row['sector_id']) > 2:
        #             sub_category = sector_dataframe.loc[sector_dataframe['sectorID'] == row['sector_id'][2], 'sectorName'].values[0]
                
        #         if len(row['sector_id']) > 3:
        #             sub_sub_category = sector_dataframe.loc[sector_dataframe['sectorID'] == row['sector_id'][3], 'sectorName'].values[0]
                
        #         # Check if the sector is already in the result
        #         existing_sector = next((res for res in result if res['sector'] == sector_name), None)
        #         if existing_sector:
        #             # If the sector is already in the result, append category and sub-category if not already present
        #             if category not in existing_sector['category']:
        #                 existing_sector['category'] += f', {category}'
        #             if sub_category not in existing_sector['sub_category']:
        #                 existing_sector['sub_category'] += f', {sub_category}'

        #             if sub_sub_category not in existing_sector['sub_sub_category']:
        #                 existing_sector['sub_sub_category'] += f', {sub_sub_category}'    
        #         else:
        #             # If the sector is not in the result, create a new entry
        #             result.append({'sector': sector_name, 'category': category, 'sub_category': sub_category, 'sub_sub_category':sub_sub_category,'sector_id':row['sector_id'][0]})
        #     final_result = list({(d['sector'], d['category'], d['sub_category'], d['sub_sub_category'],d['sector_id']): d for d in result}.values())
        #     return final_result
        # except Exception as e:
        #     # Log exceptions for debugging
        #     self.logger.exception(e)
        #     raise e     
    # async def print_sector_info(self, sector_dataframe:pd.DataFrame, parent_id_list:list, level=0,result={}):
    #     try:
    #         list_result=[]
    #         for parent_id in parent_id_list:
    #             if parent_id in sector_dataframe['sectorID'].values:
    #                 sector_info = sector_dataframe.loc[sector_dataframe['sectorID'] == parent_id, ['sectorID', 'sectorName']].iloc[0]
    #                 sectors = sector_dataframe.loc[sector_dataframe['parentID'] == parent_id, ['sectorID', 'sectorName', 'parentID']]
    #                 if not sectors.empty:
    #                     if level == 0:
    #                         result["Sector_Name"]=sector_info['sectorName']
    #                         result["Category"] = sectors['sectorName'].to_list()
    #                         await self.print_sector_info(sector_dataframe, sectors['sectorID'].unique(), level + 1,result)
    #                         list_result.append(result)
    #                         result={}
    #                         level=0 
    #                     else:
    #                         key = "Category"
    #                         sub_key = "sub_"
    #                         if key in result:
    #                             result[f"{sub_key*level}{key}"] = sectors['sectorName'].to_list()
    #                         else:
    #                             result[key] = sectors['sectorName'].to_list()
    #                         await self.print_sector_info(sector_dataframe, sectors['sectorID'].unique(), level + 1,result)
    #                         list_result.append(result)
    #                         level=0 
    #                         result={}
    #                 else:
    #                     if level == 0:
    #                         result["Sector_Name"]=sector_info['sectorName']
    #                         list_result.append(result)                          
    #         return list_result
    #     except Exception as e:
    #         self.logger.exception(e)
    #         raise e     

    async def get_cs_company(self,product_id,product_conn,master_lookup_conn):
        """
            Retrieves company information for a given product by querying related database tables,
            then processes and returns the company data in a structured format.

            Parameters:
            - product_id: ID of the product to retrieve company information for
            - product_conn: Database connection for product data
            - master_lookup_conn: Database connection for master lookup data

            Returns:
            - A dictionary containing the primary company and additional companies associated with the product,
            or a message if no companies are available for the product.
        """
        try:
            comapny_dict={}
            product_company_mapping=await self.__db_query.get_sql_table_records(["company_id","sequence_no"],"cscan_product_company_mapping","product_id",product_id,product_conn)
            if product_company_mapping:
                # Check the type and convert to a list if it's a dictionary
                if isinstance(product_company_mapping, dict):
                    product_company_mapping = [product_company_mapping]

                product_company_mapping_dataframe=pd.DataFrame(product_company_mapping)

                # Extract unique company IDs and sequence numbers
                unique_company_sequence_list = product_company_mapping_dataframe[['company_id', 'sequence_no']].drop_duplicates()
                
                # Fetch company details from the master lookup using the unique company IDs
                cs_company=await self.__db_query.get_sql_with_IN(["companyID","companyName","parentCompanyID"],"cscan_company","companyID",unique_company_sequence_list['company_id'].values.tolist(),master_lookup_conn)
                cs_company_dataframe=pd.DataFrame(cs_company)    

                # Extract the primary company name (sequence_no == 1)
                primary_company = cs_company_dataframe.loc[(cs_company_dataframe['companyID'].isin(unique_company_sequence_list.loc[unique_company_sequence_list['sequence_no'] == 1, 'company_id'].values)), 'companyName'].iloc[0]
                
                # Extract the names of additional companies (sequence_no != 1)
                Additional_companies=cs_company_dataframe.loc[
                                    cs_company_dataframe['companyID'].isin(
                                        unique_company_sequence_list.loc[unique_company_sequence_list['sequence_no'] != 1, 'company_id'].values
                                    ),
                                    'companyName'
                                ].tolist()
                comapny_dict={
                    "primary_company":primary_company,
                    "Additional_companies":Additional_companies
                }
            else:
                comapny_dict['msg']=f"company Not Available for productID {product_id}"    
            return comapny_dict
            
        except Exception as e:
            self.logger.exception(e)
            raise e     
        
    async def get_mchannel(self,product_id,product_conn,master_lookup_conn):
        try:  
            cs_product=await self.__db_query.get_sql_table_records(["mchanne_id"],"cscan_product","product_id",product_id,product_conn)
            cs_company=await self.__db_query.get_sql_table_records(["mChannelID","mChannelName"],"cscan_mchannel","mChannelID",cs_product['mchanne_id'],master_lookup_conn)
            return cs_company           
        except Exception as e:
            self.logger.exception(e)
            raise e      

    async def get_cscan_document(self,csv2_dataframe):
        try:
            raw_data={}
            filtered_content_type = csv2_dataframe[csv2_dataframe['document_placement'] != '0']['document_placement'].unique()
            filtered_content_type = [value for value in filtered_content_type if value.strip()]
            if not len(filtered_content_type):
                raw_data['document_placement'] = '200x200'
            else:    
                raw_data['document_placement'] = ', '.join(filtered_content_type) if filtered_content_type else ''
            # Remove 0 from the 'document_content_type' column
            filtered_content_type = csv2_dataframe[csv2_dataframe['document_content_type'] != '0']['document_content_type'].unique()
            filtered_content_type = [value for value in filtered_content_type if value.strip()]
            raw_data['document_content_type'] = ', '.join(filtered_content_type) if filtered_content_type else ''   
            return raw_data
        except Exception as e:
            self.logger.exception(e)
 
    async def getedc(self,csv2_product_Data:pd.DataFrame,master_lookup_conn):
        try:
            panelists_id=csv2_product_Data["panelist_id"].unique().tolist()
            postalcode=await self.__db_query.get_sql_with_IN(["postalcode"],"cscan_panelists","panelist_id",panelists_id,master_lookup_conn)
            # Extract values into a list
          
            if postalcode:
                postalcode_values = [item['postalcode'] for item in postalcode]
                fields_list=['cscan_edc.edc_name']
                table_names=['cscan_edc_postalcode','cscan_edc']
                join_conditions=['cscan_edc_postalcode.edc_id=cscan_edc.edc_id']
                cscan_panelist_info=await self.__db_query.join_tables_with_IN(fields_list,table_names,join_conditions,master_lookup_conn,'cscan_edc_postalcode.pppostalcode',postalcode_values)
                # Assuming cscan_panelist_info is a list of values you want to use as keys
               
                if cscan_panelist_info:
                    edcName = ', '.join(item['edc_name'] for item in cscan_panelist_info)
                  
                    return edcName
                else:
                    return None
            else:
                return None    
        except Exception as e:
            self.logger.exception(e)

    async def getpanalist_spendImpression(self,product_id,master_lookup_conn,mobile_digital_conn):
        try:
            mobile_digital=await self.__db_query.get_sql_table_records(["panelist_id","estimated_impressions","estimated_spend"],"cscan_mobile_digital_spend_impressions","product_id",product_id,mobile_digital_conn)
            if mobile_digital:
                # Check if mobile_digital is not a list
                if not isinstance(mobile_digital, list):
                    # Convert it to a list
                    mobile_digital = [mobile_digital]
                if mobile_digital:
                    panalist_spendImpression=[]
                    panelist_id = [item['panelist_id'] for item in mobile_digital]
                    cscanpanelist=await self.__db_query.get_sql_with_IN(["panelist_id","competi_id"],"cscan_panelists","panelist_id",panelist_id,master_lookup_conn)
                    cscanpanelist_dataframe = pd.DataFrame(cscanpanelist)
                    panalist_dict={}
                    Total_Impression = 0
                    Total_spend = 0
                    for panelist in mobile_digital:
                        Total_Impression +=float(panelist['estimated_impressions'])
                        Total_spend +=float(panelist['estimated_spend'])
                        # print(panelist['panelist_id'])
                        compti_id=cscanpanelist_dataframe.loc[cscanpanelist_dataframe['panelist_id'] == panelist['panelist_id'],'competi_id'].values[0]
                        panalist_dict = {
                            'compti_id': compti_id,
                            'estimated_impressions': round(float(panelist['estimated_impressions'])),
                            'estimated_spend': round(float(panelist['estimated_spend'])),
                        }
                        # print("panalist==",panalist_dict)
                        panalist_spendImpression.append(panalist_dict)
                    panalist_spendImpression.append({f'total_Impression': round(Total_Impression)})    
                    panalist_spendImpression.append({f'total_spend': round(Total_spend)}) 
                    return panalist_spendImpression
                else:
                    return None
        except Exception as e:
            self.logger.exception(e)

    async def doSpend(self,mail_volume_tot, document_size_byte = 0):
        try: 
            if mail_volume_tot == 0:
                return 0
            if (document_size_byte >= 2000000):
                dmspend = mail_volume_tot * (1.1593 * (pow(mail_volume_tot, -0.0382)))
            elif (document_size_byte >= 500000):
                dmspend = mail_volume_tot * (1.3648 * (pow(mail_volume_tot, -0.0783)))
            else:
                dmspend = mail_volume_tot * (1.2204 * (pow(mail_volume_tot, -0.1236)))
                
            return dmspend
        
        except Exception as e:
            self.logger.exception(e)  

    async def getcardDetails(self,Card_id:list,master_lookup_conn):
        try:
            # Convert Card_id to a NumPy array if it's not already
            Card_id_array = np.asarray(Card_id)
            if isinstance(Card_id, np.ndarray):
                Card_id_flat = Card_id_array.flatten()
            else:
                # If it's not a NumPy array, assume it's a single value
                Card_id_flat = Card_id_array
            Card_id_list = [int(val.strip()) for id_str in Card_id_flat for val in str(id_str).split(',')]
            result=await self.__db_query.get_sql_with_IN(["CardTypeName"],"cscan_card_type","CardTypeID",Card_id_list,master_lookup_conn)
            card_name_list = [entry.get('CardTypeName', '') for entry in result]
            return card_name_list
        except Exception as e:
            self.logger.exception(e)      

    async def getcardLevelType(self,CardLevel_id:list,master_lookup_conn):
        try:
            # Convert Card_id to a NumPy array if it's not already
            Card_id_array = np.asarray(CardLevel_id)
            if isinstance(CardLevel_id, np.ndarray):
                Card_id_flat = Card_id_array.flatten()
            else:
                # If it's not a NumPy array, assume it's a single value
                Card_id_flat = Card_id_array
            # Split the string by commas and convert each part to an integer using NumPy functions
            Card_id_list = [int(val.strip()) for id_str in Card_id_flat for val in str(id_str).split(',')]
            card_level=await self.__db_query.get_sql_with_IN(["CardLevelTypeName"],"cscan_cardlevel_type","CardLevelTypeID",Card_id_list,master_lookup_conn)
            card_name_list = [entry.get('CardLevelTypeName', '') for entry in card_level]
            return card_name_list
        except Exception as e:
            self.logger.exception(e)       

    async def getapplicationType(self,application_id,master_lookup_conn):
        try:
            application_name=await self.__db_query.get_sql_table_records(["ApplicationTypeName"],"cscan_application_type","ApplicationTypeID",application_id,master_lookup_conn)
            return application_name
        except Exception as e:
            self.logger.exception(e)      
    
    async def getrewardType(self,reward_id,master_lookup_conn):
        try:
             # Convert Card_id to a NumPy array if it's not already
            Reward_id_array = np.asarray(reward_id)
            if isinstance(reward_id, np.ndarray):
                reward_id_flat = Reward_id_array.flatten()
            else:
                # If it's not a NumPy array, assume it's a single value
                reward_id_flat = Reward_id_array
            # Split the string by commas and convert each part to an integer using NumPy functions
            Reward_id_list = [int(val.strip()) for id_str in reward_id_flat for val in str(id_str).split(',')]
            result=await self.__db_query.get_sql_table_records(["RewardTypeName"],"cscan_reward_type","RewardTypeID",Reward_id_list,master_lookup_conn)
            reward_name_list = [entry.get('CardTypeName', '') for entry in result]
            return reward_name_list
        except Exception as e:
            self.logger.exception(e) 

    async def getrateType(self,rate_id,master_lookup_conn):
        try:
            rate=await self.__db_query.get_sql_table_records(["RateTypeName"],"cscan_rate_type","RateTypeID",rate_id,master_lookup_conn)
            return rate
        except Exception as e:
            self.logger.exception(e)      

    async def getrewardTypeName(self,rewardtype_id,master_lookup_conn):
        try:
            reward = await self.__db_query.get_sql_table_records_reward(["RewardTypeName"], "cscan_reward_type", "RewardTypeID", rewardtype_id,master_lookup_conn)
            return reward
        except Exception as e:
            self.logger.exception(e)  

    # async def getmmuid(self,product_id,contentsite_conn):
    #     try:
    #         muid=await self.__db_query.get_sql_table_records(["muid"],"cscan_product_email","productID",product_id,contentsite_conn)
    #         if muid:
    #             return muid['muid']
    #         else:
    #             return None
    #     except Exception as e:
    #         self.logger.exception(e)              

    async def getpanelistid(self,competi_id,master_lookup_conn):
        try:
            panelist_id=await self.__db_query.get_sql_table_records(["panelist_id"],"cscan_panelists","competi_id",competi_id,master_lookup_conn)
            return panelist_id
        except Exception as e:
            self.logger.exception(e)


    async def getAffinities(self,pid,product_conn):
        try:
            affins = await self.__db_query.fetch_affinity_ids(pid,product_conn)
            affins = [row['affinityID'] for row in affins]
            return affins
        except Exception as e:
            self.logger.exception(e)

    async def getAffinNames(self, affinities,master_lookup_conn):
        # print("affins",affinities)
        affiNames=await self.__db_query.fetchAffinNames(affinities,master_lookup_conn)
        affiNames = [row['affinityName'] for row in affiNames]
        # print("affiNames",affiNames)
        return affiNames

    async def getComIds(self,pid,db1,db2):
        try:
            comIDs = await self.__db_query.fetch_com_ids(pid,db1)
            if comIDs:
                comIDs = [row['companyID'] for row in comIDs]
                comNames = await self.__db_query.fetchComNames(comIDs,db2)
                comNames = [row['companyName'] for row in comNames]
            return comNames
        except Exception as e:
            self.logger.exception(e)

    async def getpanmover(self,pid,master_lookup_conn):
        try:
            results = await self.__db_query.fetch_panelist_mover(pid,master_lookup_conn)
            for entry in results:
                entry['pm_date'] = entry['pm_date'].strftime('%Y-%m-%d')
            return results
        except Exception as e:
            self.logger.exception(e)

    
    async def getAllstates(self,master_lookup_conn):
        try:
            states = await self.__db_query.states(master_lookup_conn)
            return states
        except Exception as e:
            self.logger.exception(e)