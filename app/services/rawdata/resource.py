from fastapi import  <PERSON><PERSON>PException
from fastapi import FastAPI, Query
from db.db import Db,Db_productdb,Db_mobiledb
from typing import Optional
from .rawDataProcess import RawDataProcessor
from .affinityProcess import AffinityProcessor
from .trackdata import TrackProcess
from utils.Logger import Logger

class ProductDetail:
    def __init__(self):
        self.rawDataProcess = RawDataProcessor()
        self.logger = Logger(self.__class__.__name__).get()


    async def post(self, json_data:Optional[dict] = None):
        '''
            This function handles a POST request to retrieve and cache product data. It expects a JSON payload in the 'json_data' parameter.
            It first checks if a 'product_id' is present in the JSON data, and if not, it raises an HTTP 400 error.
            It then attempts to retrieve cached data from a Redis instance based on the 'product_id'.
            If cached data is found, it's parsed into a dictionary, and no further processing is performed.
            If no cached data is found, it calls the 'get_product_data' method to fetch and process product data.
            The resulting data is stored in Redis with the 'product_id' as the key for future use.
            Any exceptions raised during the process are logged, and an appropriate HTTP response with an error message is returned.
        '''
        try:
            master_lookup_conn = await Db().conn_competi_db()
            product_conn = await Db_productdb().conn_csv2_product_dbase()
            mobile_digital_conn = await Db_mobiledb().conn_csv2_mobile_digital_dbase()
            # contentsite_conn = await Db_contentsite().conn_contentsite_db()
            # Extract product ID from JSON data
            product_id = json_data.get('product_id')  # Assuming you have a product_id in the JSON data
            result = await self.rawDataProcess.process_raw_data(product_id,master_lookup_conn, product_conn,mobile_digital_conn)
                    
            return result  # Directly return the dictionary
        except Exception as e:
            self.logger.exception(e)
            raise HTTPException(status_code=500, detail="Something Went Wrong: " + str(e))




class AffinityDetails:
    def __init__(self):
        self.dataProcess = AffinityProcessor()
        self.logger = Logger(self.__class__.__name__).get()


    async def post(self, json_data:Optional[dict] = None):
        try:
            master_lookup_conn = await Db().conn_competi_db()
            product_conn = await Db_productdb().conn_csv2_product_dbase()
            competi_id = json_data.get('competi_id')
            response =await self.dataProcess.fetch_data(competi_id,master_lookup_conn, product_conn)
            if response:
                return response
            else:
                return {"message": "No Data Found", "status_code": 404}
        except Exception as e:
            self.logger.exception(e)
            return {"message": "Something Went Wrong", "status_code": 500}

class TrackDetails:
    def __init__(self):
        self.dataProcess = TrackProcess()
        self.logger = Logger(self.__class__.__name__).get()


    async def post(self, json_data:Optional[dict] = None):
        try:
            product_conn = await Db_productdb().conn_csv2_product_dbase()
            tracking_id=json_data.get('tracking_id')
            response =await self.dataProcess.fetch_track_data(tracking_id,product_conn)
            return response
        except Exception as e:
            self.logger.exception(e)
            raise HTTPException(status_code=500, detail="Something Went Wrong: " + str(e))

