from time import time
from .media_channel import MediaProcessorFactory
from query_functions.querys import Querys
from .model import Model
from .sector import SectorProcessorFactory
from utils.Logger import Logger
import numpy as np
import json
import numpy as np

def convert_numpy_types(data):
    """
        Recursively converts numpy numeric types in a dictionary or list
        to native Python types.
    """
    if isinstance(data, dict):
        return {k: convert_numpy_types(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [convert_numpy_types(item) for item in data]
    elif isinstance(data, np.integer):
        return int(data)
    elif isinstance(data, np.floating):
        return float(data)
    else:
        return data

class RawDataProcessor:
    def __init__(self):
        self.logger =  Logger(self.__class__.__name__).get()
        self.model=Model()

    async def process_raw_data(self,product_id,master_lookup_conn, product_conn,mobile_digital_conn):
        """
            Processes raw data for a given product by retrieving and consolidating data from various sources.

            Parameters:
            - product_id: ID of the product to process
            - master_lookup_conn: Database connection for master lookup
            - product_conn: Database connection for product data
            - mobile_digital_conn: Database connection for mobile digital data

            Returns:
            - A dictionary with consolidated raw data or an error message if any step fails.
        """
        try:
            # Log the start time
            start_time = time()
            self.logger.info("Processing raw data started at " + str(start_time))
            raw_data={}

            # Get company details
            cs_company_details=await self.model.get_cs_company(product_id,product_conn,master_lookup_conn)
            if 'msg' in cs_company_details:
                pass
            else:
                raw_data.update(cs_company_details)
            
            # Get sector details
            sector_name_data=await self.model.get_sector(product_id,product_conn,master_lookup_conn)
            raw_data['Sector_detail']=sector_name_data
            
            # Get media channel name
            media_channel_name=await self.model.get_mchannel(product_id,product_conn,master_lookup_conn)
            sector_name=[result.get('sector', None) for result in sector_name_data]
            media_channel = media_channel_name['mChannelName']
            raw_data.update(media_channel_name)
            # Create and use media processor
            media_processor = MediaProcessorFactory.create_media_processor(media_channel)
            if media_processor:
                csv2_product_Data, result_rawdata=await media_processor.process(product_conn,master_lookup_conn,product_id,mobile_digital_conn)
                if result_rawdata is not None:
                    if isinstance(result_rawdata, list) and len(result_rawdata) > 0:
                        # If result_rawdata is a non-empty list of dictionaries, update raw_data with the first element
                        raw_data.update(result_rawdata[0])
                    else:
                        raw_data.update(result_rawdata)
            raw_data['product_headline'] = csv2_product_Data['product_headline'].unique()[0]     
            # Process data for each sector
            for sector in sector_name:
                sector_processor = SectorProcessorFactory.create_sector_processor(sector)
                if sector_processor:
                    sector_data=await sector_processor.process(csv2_product_Data,master_lookup_conn)
                    raw_data.update(sector_data)
                
                # Additional processing based on media channel and sector
                if media_channel == 'Print':
                    raw_data['product_headline']=csv2_product_Data['product_headline'].unique()[0]
                    if sector =='Retail':
                        if "affinity_id" in csv2_product_Data.columns:
                            affinity=await self.model.get_affinity_category(master_lookup_conn,csv2_product_Data)  
                            raw_data.update(affinity)          
                else:
                    if media_channel != "Search Engine Marketing": 
                        raw_data['product_headline']=csv2_product_Data['product_headline'].unique()[0]  
                    else:
                        raw_data['product_headline']=csv2_product_Data['product_headline'].unique()[0]

                if 'worksite_voluntary' in csv2_product_Data:
                    raw_data['worksite_voluntary']=int(csv2_product_Data['worksite_voluntary'].unique()[0])
                
                # if 'affinity_association' in csv2_product_Data:
                #     raw_data['affinity_association']=int(csv2_product_Data['affinity_association'].unique()[0])                 
                category = await self.model.get_affinity_category(master_lookup_conn, csv2_product_Data)
                if category and 'affinityName' in category[0]:
                    print("affinityName  exists")
                    raw_data['affinity_association'] = 'Yes'
                else:
                    print("affinityName does not exist")
                    raw_data['affinity_association'] = 'No'

            # Update raw_data with remaining fields
            value = csv2_product_Data['dts_val'].unique()[0]
            if isinstance(value, (int, float, np.integer)):
                raw_data['dts_val'] = int(value)
            else:
                raw_data['dts_val'] = csv2_product_Data['dts_val'].unique()[0]
            raw_data['entry_id']=csv2_product_Data['entry_id'].unique()[0]
            if raw_data.get('entry_id') == 0 or raw_data.get('entry_id').strip() == "":
                raw_data['entry_id'] = ""
            else:            
                # Extract the date parts from entry_id
                date_parts = raw_data['entry_id'].split('-')
                year = date_parts[0]
                month = date_parts[1]
                day = date_parts[2]
                # Create the formatted date
                formatted_date = f"{month}/{day}/{year}"
                # Update the rawdata dictionary with the formatted date
                raw_data['date_updated'] = formatted_date
            raw_data['approved_date']=str(csv2_product_Data['approved_date'].unique()[0])
            raw_data['mpanel_id']=int(csv2_product_Data['mpanel_id'].unique()[0])    
            raw_data['is_qa']=int(csv2_product_Data['is_qa'].unique()[0])    
            finish_time = time() - start_time
            self.logger.info("Processing raw data completed in " + str(finish_time) + " seconds")
            raw_data = convert_numpy_types(raw_data)
            return raw_data
        except Exception as e:
            self.logger.exception(e)