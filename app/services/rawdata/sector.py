from query_functions.querys import Querys
from pydantic_models.pydantic_classes import *
from .model import Model


class SectorProcessor:   
    def __init__(self):
        self.__db_query=Querys()
        self.model=Model()   

    async def process(self, csv2_product_data,  master_lookup_conn):
        pass

class AutomotiveProcessor(SectorProcessor):
    async def process(self, csv2_product_data,  master_lookup_conn):
        # Implementation specific to Retail sector processing
        rawdata={}
        master_look_up_query=[
                [["age_pname"], "cscan_age_product", "age_pID", "age_id"],
                [["incomeName"], "cscan_incometype", "incomeID", "income_id"],
                [["text_value as language"], "cscan_campaign_language", "id", "language_id"],
                [["mTypeName"],"cscan_mtype", "mTypeID ", "mtype_id"],                
                [["mPanelName"],"cscan_mpanel", "mPanelID", "mpanel_id"],
                [["stateName"],"cscan_state", "stateID", "state_id"],
                [["fa_name"],"cscan_face_amount", "fa_id", "face_id"],
                [["ridersName"],"cscan_riders", "ridersID", "rider_id"],
                [["tl_name"],"cscan_term_length", "tl_id", "term_id"],
                [["delmethname"],"cscan_delivery_method", "delmethid", "delivery_method_id"],
                [["type"],"cscan_agent_communication", "ID", "agent_communication_id"]
                ]
        rawdata=await self.model.get_masterlookup_data(master_look_up_query,csv2_product_data,master_lookup_conn) 
        return rawdata

class BankingProcessor(SectorProcessor):
    async def process(self, csv2_product_data,  master_lookup_conn):
        # Implementation specific to Mortgage & Loan sector processing
        rawdata={}
        master_look_up_query=[
                [["age_pname"], "cscan_age_product", "age_pID", "age_id"],
                #[["RewardTypeName"], "cscan_reward_type", "RewardTypeID", "reward_id"],
                [["ancillary_name"],"cscan_ancillary_product", "ancillary_id", "ancillary_product_id"],
                [["incomeName"], "cscan_incometype", "incomeID", "income_id"],
                [["text_value as language"], "cscan_campaign_language", "id", "language_id"],
                [["mTypeName"],"cscan_mtype", "mTypeID ", "mtype_id"],                
                [["mPanelName"],"cscan_mpanel", "mPanelID", "mpanel_id"],
                [["tl_name"],"cscan_term_length", "tl_id", "term_id"],
                [["fa_name"],"cscan_face_amount", "fa_id", "face_id"],
                [["stateName"],"cscan_state", "stateID", "state_id"],
                [["ridersName"],"cscan_riders", "ridersID", "rider_id"],
                [["CardTypeName"],"cscan_card_type", "CardTypeID ", "Card_id"],
                [["delmethname"],"cscan_delivery_method", "delmethid", "delivery_method_id"],
                [["type"],"cscan_agent_communication", "ID", "agent_communication_id"]
                ]
        rawdata=await self.model.get_masterlookup_data(master_look_up_query,csv2_product_data,master_lookup_conn) 
        
        banking_data =await Banking.from_csv2_product_data(csv2_product_data,rawdata,master_lookup_conn)
        result_dict = banking_data.dict()
        if "CardTypeName" in rawdata:
                rawdata.pop("CardTypeName")
        rawdata["banking_details"]=result_dict  
        rawdata['fee_product']=int(csv2_product_data['fee_product'].unique()[0])
        return rawdata

class CustomerServiceProcessor(SectorProcessor):

    async def process(self, csv2_product_data,  master_lookup_conn):
        # Implementation specific to Retail sector processing
        rawdata={}
        master_look_up_query=[
                [["age_pname"], "cscan_age_product", "age_pID", "age_id"],
                [["ancillary_name"],"cscan_ancillary_product", "ancillary_id", "ancillary_product_id"],
                [["incomeName"], "cscan_incometype", "incomeID", "income_id"],
                [["text_value as language"], "cscan_campaign_language", "id", "language_id"],
                [["mTypeName"],"cscan_mtype", "mTypeID ", "mtype_id"],      
                [["tl_name"],"cscan_term_length", "tl_id", "term_id"],          
                [["mPanelName"],"cscan_mpanel", "mPanelID", "mpanel_id"],
                [["fa_name"],"cscan_face_amount", "fa_id", "face_id"],
                [["stateName"],"cscan_state", "stateID", "state_id"],
                [["ridersName"],"cscan_riders", "ridersID", "rider_id"],
                [["delmethname"],"cscan_delivery_method", "delmethid", "delivery_method_id"],
                [["type"],"cscan_agent_communication", "ID", "agent_communication_id"]
                ]
        rawdata=await self.model.get_masterlookup_data(master_look_up_query,csv2_product_data,master_lookup_conn) 
        return rawdata

class CreditCardsProcessor(SectorProcessor):
    async def process(self, csv2_product_data,  master_lookup_conn):
        # Implementation specific to Mortgage & Loan sector processing
        rawdata={}
        master_look_up_query=[
                [["age_pname"], "cscan_age_product", "age_pID", "age_id"],
                [["ancillary_name"],"cscan_ancillary_product", "ancillary_id", "ancillary_product_id"],
                [["incomeName"], "cscan_incometype", "incomeID", "income_id"],
                [["text_value as language"], "cscan_campaign_language", "id", "language_id"],
                [["mTypeName"],"cscan_mtype", "mTypeID ", "mtype_id"],   
                [["fa_name"],"cscan_face_amount", "fa_id", "face_id"],             
                [["mPanelName"],"cscan_mpanel", "mPanelID", "mpanel_id"],
                [["delmethname"],"cscan_delivery_method", "delmethid", "delivery_method_id"],
                [["stateName"],"cscan_state", "stateID", "state_id"],
                [["tl_name"],"cscan_term_length", "tl_id", "term_id"],
                [["ridersName"],"cscan_riders", "ridersID", "rider_id"],
                [["type"],"cscan_agent_communication", "ID", "agent_communication_id"]
                ]
        rawdata=await self.model.get_masterlookup_data(master_look_up_query,csv2_product_data,master_lookup_conn) 
        rawdata['fee_product']=int(csv2_product_data['fee_product'].unique()[0])
        payment_data = await PaymentCardDetails.from_csv2_product_data(csv2_product_data,master_lookup_conn)
        result_dict = payment_data.dict()
        rawdata["payment_cards"]=result_dict
        creditaccess=await CreditCardsAccess.from_csv2_product_data(csv2_product_data,master_lookup_conn)
        result_dict = creditaccess.dict()
        rawdata["credit_cards_access"]=result_dict
        return rawdata

class EnergyProcessor(SectorProcessor):

    async def process(self, csv2_product_data,  master_lookup_conn):
        # Implementation specific to Retail sector processing
        rawdata={}
        master_look_up_query=[
                [["age_pname"], "cscan_age_product", "age_pID", "age_id"],
                [["incomeName"], "cscan_incometype", "incomeID", "income_id"],
                [["text_value as language"], "cscan_campaign_language", "id", "language_id"],
                [["mTypeName"],"cscan_mtype", "mTypeID ", "mtype_id"],                
                [["mPanelName"],"cscan_mpanel", "mPanelID", "mpanel_id"],
                [["fa_name"],"cscan_face_amount", "fa_id", "face_id"],
                [["delmethname"],"cscan_delivery_method", "delmethid", "delivery_method_id"],
                [["stateName"],"cscan_state", "stateID", "state_id"],
                [["tl_name"],"cscan_term_length", "tl_id", "term_id"],
                [["ridersName"],"cscan_riders", "ridersID", "rider_id"],
                [["type"],"cscan_agent_communication", "ID", "agent_communication_id"]
                ]
        rawdata=await self.model.get_masterlookup_data(master_look_up_query,csv2_product_data,master_lookup_conn) 
        rawdata['fee_product']=int(csv2_product_data['fee_product'].unique()[0])
        energy_data = await Energy.from_csv2_product_data(csv2_product_data,master_lookup_conn)
        result_dict = energy_data.dict()
        rawdata['energy_details']=result_dict
        return rawdata

class HR_Payroll_HCM_PEOProcessor(SectorProcessor):
    
    async def process(self, csv2_product_data,  master_lookup_conn):
        # Implementation specific to Mortgage & Loan sector processing
        rawdata={}
        master_look_up_query=[
                [["age_pname"], "cscan_age_product", "age_pID", "age_id"],
                [["incomeName"], "cscan_incometype", "incomeID", "income_id"],
                [["text_value as language"], "cscan_campaign_language", "id", "language_id"],
                [["mTypeName"],"cscan_mtype", "mTypeID ", "mtype_id"],    
                [["fa_name"],"cscan_face_amount", "fa_id", "face_id"],           
                [["mPanelName"],"cscan_mpanel", "mPanelID", "mpanel_id"],
                [["delmethname"],"cscan_delivery_method", "delmethid", "delivery_method_id"],
                [["stateName"],"cscan_state", "stateID", "state_id"],
                [["tl_name"],"cscan_term_length", "tl_id", "term_id"],
                [["ridersName"],"cscan_riders", "ridersID", "rider_id"],
                [["type"],"cscan_agent_communication", "ID", "agent_communication_id"]
                ]
        rawdata=await self.model.get_masterlookup_data(master_look_up_query,csv2_product_data,master_lookup_conn) 
        return rawdata

class InsuranceProcessor(SectorProcessor):
    async def process(self, csv2_product_data,  master_lookup_conn):
        # Implementation specific to Retail sector processing
        rawdata={}
        master_look_up_query=[
                [["age_pname"], "cscan_age_product", "age_pID", "age_id"],
                [["incomeName"], "cscan_incometype", "incomeID", "income_id"],
                [["name"],"cscan_group_size", "id", "group_size_id"],
                [["fa_name"],"cscan_face_amount", "fa_id", "face_id"],
                [["oo_name"],"cscan_offer_origin", "oo_id", "offer_origin_id"],
                [["text_value as language"], "cscan_campaign_language", "id", "language_id"],
                [["mTypeName"],"cscan_mtype", "mTypeID ", "mtype_id"],      
                [["ridersName"],"cscan_riders", "ridersID", "rider_id"],          
                [["mPanelName"],"cscan_mpanel", "mPanelID", "mpanel_id"],
                [["delmethname"],"cscan_delivery_method", "delmethid", "delivery_method_id"],
                [["stateName"],"cscan_state", "stateID", "state_id"],
                [["tl_name"],"cscan_term_length", "tl_id", "term_id"],
                [["type"],"cscan_agent_communication", "ID", "agent_communication_id"]
                ]
        rawdata=await self.model.get_masterlookup_data(master_look_up_query,csv2_product_data,master_lookup_conn) 
        rawdata['is_prescreen']=int(csv2_product_data['is_prescreen'].unique()[0])
        rawdata['prescription']=int(csv2_product_data['prescription'].unique()[0])
        rawdata['fee_product']=int(csv2_product_data['fee_product'].unique()[0])
        return rawdata

class InvestmentAnnuitiesProcessor(SectorProcessor):
    async def process(self, csv2_product_data,  master_lookup_conn):
        # Implementation specific to Mortgage & Loan sector processing
        rawdata={}
        master_look_up_query=[
                [["age_pname"], "cscan_age_product", "age_pID", "age_id"],
                [["incomeName"], "cscan_incometype", "incomeID", "income_id"],
                [["text_value as language"], "cscan_campaign_language", "id", "language_id"],
                [["name"],"cscan_group_size", "id", "group_size_id"],
                [["oo_name"],"cscan_offer_origin", "oo_id", "offer_origin_id"],
                [["fa_name"],"cscan_face_amount", "fa_id", "face_id"],
                [["mTypeName"],"cscan_mtype", "mTypeID ", "mtype_id"],     
                [["ridersName"],"cscan_riders", "ridersID", "rider_id"],
                [["tl_name"],"cscan_term_length", "tl_id", "term_id"],           
                [["mPanelName"],"cscan_mpanel", "mPanelID", "mpanel_id"],
                [["delmethname"],"cscan_delivery_method", "delmethid", "delivery_method_id"],
                [["stateName"],"cscan_state", "stateID", "state_id"],
                [["type"],"cscan_agent_communication", "ID", "agent_communication_id"]
                ]
        rawdata=await self.model.get_masterlookup_data(master_look_up_query,csv2_product_data,master_lookup_conn) 
        return rawdata

class MortgageLoanProcessor(SectorProcessor):
    async def process(self, csv2_product_data,  master_lookup_conn):
        # Implementation specific to Retail sector processing
        rawdata={}
        master_look_up_query=[
                [["age_pname"], "cscan_age_product", "age_pID", "age_id"],
                [["incomeName"], "cscan_incometype", "incomeID", "income_id"],
                [["text_value as language"], "cscan_campaign_language", "id", "language_id"],
                [["mTypeName"],"cscan_mtype", "mTypeID ", "mtype_id"],   
                [["delmethname"],"cscan_delivery_method", "delmethid", "delivery_method_id"],             
                [["mPanelName"],"cscan_mpanel", "mPanelID", "mpanel_id"],
                [["fa_name"],"cscan_face_amount", "fa_id", "face_id"],
                [["ridersName"],"cscan_riders", "ridersID", "rider_id"],
                [["stateName"],"cscan_state", "stateID", "state_id"],
                [["tl_name"],"cscan_term_length", "tl_id", "term_id"],
                [["type"],"cscan_agent_communication", "ID", "agent_communication_id"]
                ]
        rawdata=await self.model.get_masterlookup_data(master_look_up_query,csv2_product_data,master_lookup_conn) 
        rawdata['fee_product']=int(csv2_product_data['fee_product'].unique()[0])
        mortgage_loan_data = await MortgageLoan.from_csv2_product_data(csv2_product_data,master_lookup_conn)
        result_dict = mortgage_loan_data.dict()
        rawdata['mortgage_Loan_detail']=result_dict
        return rawdata

class NonProfitProcessor(SectorProcessor):
    async def process(self, csv2_product_data,  master_lookup_conn):
        # Implementation specific to Mortgage & Loan sector processing
        rawdata={}
        master_look_up_query=[
                [["age_pname"], "cscan_age_product", "age_pID", "age_id"],
                [["incomeName"], "cscan_incometype", "incomeID", "income_id"],
                [["text_value as language"], "cscan_campaign_language", "id", "language_id"],
                [["mTypeName"],"cscan_mtype", "mTypeID ", "mtype_id"],                
                [["mPanelName"],"cscan_mpanel", "mPanelID", "mpanel_id"],
                [["fa_name"],"cscan_face_amount", "fa_id", "face_id"],
                [["tl_name"],"cscan_term_length", "tl_id", "term_id"],
                [["ridersName"],"cscan_riders", "ridersID", "rider_id"],
                [["delmethname"],"cscan_delivery_method", "delmethid", "delivery_method_id"],
                [["stateName"],"cscan_state", "stateID", "state_id"],
                [["type"],"cscan_agent_communication", "ID", "agent_communication_id"]
                ]
        rawdata=await self.model.get_masterlookup_data(master_look_up_query,csv2_product_data,master_lookup_conn) 
        return rawdata

class PharmaceuticalProcessor(SectorProcessor):
    async def process(self, csv2_product_data,  master_lookup_conn):
        # Implementation specific to Retail sector processing
        rawdata={}
        master_look_up_query=[
                [["age_pname"], "cscan_age_product", "age_pID", "age_id"],
                [["incomeName"], "cscan_incometype", "incomeID", "income_id"],
                [["text_value as language"], "cscan_campaign_language", "id", "language_id"],
                [["mTypeName"],"cscan_mtype", "mTypeID ", "mtype_id"],                
                [["mPanelName"],"cscan_mpanel", "mPanelID", "mpanel_id"],
                [["tl_name"],"cscan_term_length", "tl_id", "term_id"],
                [["fa_name"],"cscan_face_amount", "fa_id", "face_id"],
                [["delmethname"],"cscan_delivery_method", "delmethid", "delivery_method_id"],
                [["stateName"],"cscan_state", "stateID", "state_id"],
                [["ridersName"],"cscan_riders", "ridersID", "rider_id"],
                [["type"],"cscan_agent_communication", "ID", "agent_communication_id"]
                ]
        rawdata=await self.model.get_masterlookup_data(master_look_up_query,csv2_product_data,master_lookup_conn) 
        return rawdata

class RetailProcessor(SectorProcessor):
    async def process(self, csv2_product_data,  master_lookup_conn):
        # Implementation specific to Mortgage & Loan sector processing
        rawdata={}
        master_look_up_query=[
                [["age_pname"], "cscan_age_product", "age_pID", "age_id"],
                [["incomeName"], "cscan_incometype", "incomeID", "income_id"],
                [["text_value as language"], "cscan_campaign_language", "id", "language_id"],
                [["mTypeName"],"cscan_mtype", "mTypeID ", "mtype_id"],
                [["tl_name"],"cscan_term_length", "tl_id", "term_id"],                
                [["mPanelName"],"cscan_mpanel", "mPanelID", "mpanel_id"],
                [["fa_name"],"cscan_face_amount", "fa_id", "face_id"],
                [["ridersName"],"cscan_riders", "ridersID", "rider_id"],
                [["stateName"],"cscan_state", "stateID", "state_id"],
                [["delmethname"],"cscan_delivery_method", "delmethid", "delivery_method_id"],
                [["type"],"cscan_agent_communication", "ID", "agent_communication_id"]
                ]
        rawdata=await self.model.get_masterlookup_data(master_look_up_query,csv2_product_data,master_lookup_conn) 
        rawdata['retail_detail']={
                'RCreditCardMentioned':'No' if csv2_product_data['RCreditCardMentioned'].unique()[0] == 0 else 'Yes',
                       }
        # rawdata['RCreditCardMentioned']=int(csv2_product_data['RCreditCardMentioned'].unique()[0])
        return rawdata

class ShippingProcessor(SectorProcessor):
    async def process(self, csv2_product_data,  master_lookup_conn):
        # Implementation specific to Mortgage & Loan sector processing
        rawdata={}
        master_look_up_query=[
                [["age_pname"], "cscan_age_product", "age_pID", "age_id"],
                [["incomeName"], "cscan_incometype", "incomeID", "income_id"],
                [["text_value as language"], "cscan_campaign_language", "id", "language_id"],
                [["mTypeName"],"cscan_mtype", "mTypeID ", "mtype_id"],                
                [["mPanelName"],"cscan_mpanel", "mPanelID", "mpanel_id"],
                [["fa_name"],"cscan_face_amount", "fa_id", "face_id"],
                [["ridersName"],"cscan_riders", "ridersID", "rider_id"],
                [["delmethname"],"cscan_delivery_method", "delmethid", "delivery_method_id"],
                [["stateName"],"cscan_state", "stateID", "state_id"],
                [["tl_name"],"cscan_term_length", "tl_id", "term_id"],
                [["type"],"cscan_agent_communication", "ID", "agent_communication_id"]
                ]
        rawdata=await self.model.get_masterlookup_data(master_look_up_query,csv2_product_data,master_lookup_conn) 
        return rawdata

class TelecomProcessor(SectorProcessor):
    async def process(self, csv2_product_data,  master_lookup_conn):
        # Implementation specific to Mortgage & Loan sector processing
        rawdata={}
        master_look_up_query=[
                [["age_pname"], "cscan_age_product", "age_pID", "age_id"],
                [["incomeName"], "cscan_incometype", "incomeID", "income_id"],
                [["text_value as language"], "cscan_campaign_language", "id", "language_id"],
                [["mTypeName"],"cscan_mtype", "mTypeID ", "mtype_id"],                
                [["mPanelName"],"cscan_mpanel", "mPanelID", "mpanel_id"],
                [["delmethname"],"cscan_delivery_method", "delmethid", "delivery_method_id"],
                [["stateName"],"cscan_state", "stateID", "state_id"],
                [["ridersName"],"cscan_riders", "ridersID", "rider_id"],
                [["fa_name"],"cscan_face_amount", "fa_id", "face_id"],
                [["tl_name"],"cscan_term_length", "tl_id", "term_id"],
                [["type"],"cscan_agent_communication", "ID", "agent_communication_id"]
                ]
        rawdata=await self.model.get_masterlookup_data(master_look_up_query,csv2_product_data,master_lookup_conn) 
        rawdata['fee_product']=int(csv2_product_data['fee_product'].unique()[0])
        # print("telcome===============",csv2_product_data['telecom'])
        # if 'telecom' in csv2_product_data and csv2_product_data['telecom'].iloc[0] != 0:
        telecom_data = Telecom.from_csv2_product_data(csv2_product_data)
        result_dict = telecom_data.dict()
        rawdata['Telecom_detail']=result_dict
        return rawdata

class TravelLeisureProcessor(SectorProcessor):
    async def process(self, csv2_product_data,  master_lookup_conn):
        # Implementation specific to Mortgage & Loan sector processing
        raw_data={}
        master_look_up_query=[
                [["age_pname"], "cscan_age_product", "age_pID", "age_id"],
                [["incomeName"], "cscan_incometype", "incomeID", "income_id"],
                [["text_value as language"], "cscan_campaign_language", "id", "language_id"],
                [["mTypeName"],"cscan_mtype", "mTypeID ", "mtype_id"],                
                [["mPanelName"],"cscan_mpanel", "mPanelID", "mpanel_id"],
                [["fa_name"],"cscan_face_amount", "fa_id", "face_id"],
                [["ridersName"],"cscan_riders", "ridersID", "rider_id"],
                [["delmethname"],"cscan_delivery_method", "delmethid", "delivery_method_id"],
                [["stateName"],"cscan_state", "stateID", "state_id"],
                [["tl_name"],"cscan_term_length", "tl_id", "term_id"],
                [["type"],"cscan_agent_communication", "ID", "agent_communication_id"]
                ]
        rawdata=await self.model.get_masterlookup_data(master_look_up_query,csv2_product_data,master_lookup_conn) 
        raw_data=rawdata
        travel_data = await Travel.from_csv2_product_data(csv2_product_data,master_lookup_conn)
        result_dict = travel_data.dict()
        rawdata['fee_product']=int(csv2_product_data['fee_product'].unique()[0])
        raw_data['TravelLeisure_details']=result_dict    
        return raw_data

class SectorProcessorFactory:
    @staticmethod
    def create_sector_processor(sector):
        processors = {
            'Automotive': AutomotiveProcessor,
            'Banking': BankingProcessor,
            'Consumer Services': CustomerServiceProcessor,
            'Credit Cards': CreditCardsProcessor,
            'Energy': EnergyProcessor,
            'HR/Payroll/HCM/PEO': HR_Payroll_HCM_PEOProcessor,
            'Insurance': InsuranceProcessor,
            'Investments/Annuities': InvestmentAnnuitiesProcessor,
            'Non-Profit': NonProfitProcessor,
            'Pharmaceutical': PharmaceuticalProcessor,
            'Retail': RetailProcessor,
            'Shipping': ShippingProcessor,
            'Telecom': TelecomProcessor,
            'Travel & Leisure': TravelLeisureProcessor,
            'Mortgage & Loan': MortgageLoanProcessor,
            # Add more sectors as needed
        }
        processor_class = processors.get(sector)
        return processor_class() if processor_class else None