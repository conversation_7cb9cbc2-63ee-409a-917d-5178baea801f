from time import time
from .media_channel import MediaProcessorFactory
from query_functions.querys import Querys
from .model import Model
from .sector import SectorProcessorFactory
from utils.Logger import Logger
import numpy as np



class AffinityProcessor:
    def __init__(self):
        self.logger =  Logger(self.__class__.__name__).get()
        self.model=Model()

    async def fetch_data(self, competi_id,master_lookup_conn, product_conn):
        try:
            pid = await self.model.getpanelistid(competi_id,master_lookup_conn)
            # print("pid",pid)
            affinNames = []
            coms = []
            panMover = []
            cos = []
            if pid:
                affinities = await self.model.getAffinities(pid["panelist_id"],product_conn)
            if affinities:
                affinNames = await self.model.getAffinNames(affinities,master_lookup_conn)
            if pid:
                coms = await self.model.getComIds(pid["panelist_id"],product_conn,master_lookup_conn)
            if pid:
                panMover = await self.model.getpanmover(pid["panelist_id"],master_lookup_conn)
            if panMover:
                states = await self.model.getAllstates(master_lookup_conn)
                states_dict = {state["stateID"]: state["stateName"] for state in states}
                for pan in panMover:
                    pmdate = pan["pm_date"]
                    pc1 = pan["postalcode1"]
                    pc2 = pan["postalcode2"]
                    state1_name = states_dict.get(pan["stateID1"], "Unknown State")
                    state2_name = states_dict.get(pan["stateID2"], "Unknown State")
                    cos.append(f"{pmdate} [{state1_name} {pc1} > {state2_name} {pc2}]")


            data = dict()
            data["Affinity_Names"]=affinNames
            data["Company_Names"]=coms
            if cos:
                data["AddressChangeHistory"]=cos # Address Change
            result = dict()
            result["data"] = data
            result["Message"] = "Successfull"
            result["status_code"] = "200"
            # print(result)
            return result
        except Exception as e:
            print("error",str(e))
            self.logger.exception(e)
            return None

