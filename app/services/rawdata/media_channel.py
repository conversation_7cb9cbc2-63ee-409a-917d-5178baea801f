from query_functions.querys import Querys
import pandas as pd
from .model import Model
from pydantic_models.pydantic_classes import *
from math import floor
from utils.Logger import Logger

class MediaProcessor:
    def __init__(self):
        self.db_query=Querys()
        self.model=Model()
        self.logger = Logger(self.__class__.__name__).get()

    async def process(self, product_conn,master_lookup_conn,product_id,mobile_digital_conn):
        pass

class SocialMediaProcessor(MediaProcessor):
    
    async def process(self, product_conn,master_lookup_conn,product_id,mobile_digital_conn):
        # Implementation specific to Social Media processing
        try:
            rawdata={}
            fields_list=["cscan_product.approved_date","cscan_product.fee_product","cscan_product.is_prescreen","cscan_product.prescription","cscan_product.offer_origin_id","cscan_product.mpanel_id","cscan_product.product_headline","cscan_product.entry_id","cscan_product.is_qa","cscan_banking.DebitCardMentioned","cscan_banking.BankingCardType As Card_id",
                            "cscan_banking.BankingRewardsProgramEmphasis","cscan_banking.BankingRewardsProgram","cscan_banking.MinimumDeposit","cscan_payment_cards.*","cscan_energy.ERateType","cscan_energy.EOfferPrice","cscan_energy.ECancelFee","cscan_retail.RCreditCardMentioned","cscan_energy.productID as energy","cscan_banking.productID as banking","cscan_payment_cards.productID as payment",
                            "cscan_document_text_search.dts_val","cscan_product.socialmedia_adtype","cscan_product_agent_communication_mapping.agent_communication_id","cscan_telecom.*","cscan_credit_access_checks.*","cscan_mortgage_loan.productID as mortgage_loan","cscan_travel_leisure.productID as travel",
                            "cscan_travel_leisure.TLDebitCardMentioned","cscan_travel_leisure.TLCreditCardMentioned","cscan_travel_leisure.TLCardNetwork","cscan_product_state_mapping.state_id","cscan_product_affinity_mapping.afinity_id","cscan_telecom.productID as telecom","cscan_credit_access_checks.productID as credit",
                            "cscan_social_media_link.external_link","cscan_social_media_link.external_updates","cscan_social_media_link.external_fans","cscan_product.added_to_database","cscan_mortgage_loan.*","cscan_product_groupsize_mapping.group_size_id"]
            table_names = ["cscan_product", "cscan_product_agent_communication_mapping","cscan_document_text_search","cscan_social_media_link","cscan_banking","cscan_payment_cards","cscan_energy",
                        "cscan_retail","cscan_telecom","cscan_travel_leisure","cscan_product_state_mapping","cscan_mortgage_loan","cscan_product_groupsize_mapping","cscan_product_affinity_mapping","cscan_credit_access_checks"]
            join_condition=["cscan_product.product_id=cscan_product_agent_communication_mapping.product_id","cscan_product.product_id=cscan_document_text_search.productID",
                            "cscan_product.product_id = cscan_social_media_link.product_id","cscan_product.product_id=cscan_banking.productID","cscan_product.product_id=cscan_payment_cards.productID"
                            ,"cscan_product.product_id=cscan_energy.productID","cscan_product.product_id=cscan_retail.productID","cscan_product.product_id=cscan_telecom.productID"
                            ,"cscan_product.product_id=cscan_travel_leisure.productID","cscan_product.product_id=cscan_product_state_mapping.product_id","cscan_product.product_id=cscan_mortgage_loan.productID"
                            ,"cscan_product.product_id=cscan_product_groupsize_mapping.product_id","cscan_product.product_id=cscan_product_affinity_mapping.product_id","cscan_product.product_id=cscan_credit_access_checks.productID"]
            # Fetch data from the 'product_conn' database
            csv2_product_data = await self.db_query.join_tables(fields_list, table_names, join_condition,product_conn,"cscan_product.product_id",product_id)
        
            # Convert the fetched data to a DataFrame
            csv2_dataframe = pd.DataFrame(csv2_product_data).fillna(0)
            media_data=SocialMedia.from_csv2_product_data(csv2_dataframe)
            result_dict = media_data.dict()
            if result_dict:
                rawdata['Network_Name']=result_dict['Network_Name']
                rawdata['No_of_tweets']=result_dict['No_of_tweets']
                rawdata['No_of_followers']=result_dict['No_of_followers']
                rawdata['No_of_fans']=result_dict['No_of_fans']
                rawdata['external_link']=result_dict['external_link']
            master_loopup_data=await self.db_query.get_sql_table_records(["text_value as media_adtype"],"cscan_socialmedia_adtype","id",csv2_dataframe['socialmedia_adtype'].unique()[0],master_lookup_conn) 
            if master_loopup_data is not None:
                rawdata.update(master_loopup_data)

            return csv2_dataframe ,rawdata
        except Exception as e:
            self.logger.exception(e)
            

class EmailProcessor(MediaProcessor):
    async def process(self, product_conn,master_lookup_conn,product_id,mobile_digital_conn):
        # Implementation specific to Email processing
        try:
            rawdata={}
            fields_list=["cscan_product.approved_date","cscan_product.fee_product","cscan_product.prescription","cscan_product.offer_origin_id","cscan_product.is_prescreen","cscan_product.worksite_voluntary","cscan_product.mtype_id","cscan_product.mpanel_id","cscan_product.product_headline","cscan_product.entry_id",
                            "cscan_product.is_qa","cscan_banking.DebitCardMentioned","cscan_banking.MinimumDeposit","cscan_banking.BankingCardType As Card_id","cscan_product_face_mapping.face_id","cscan_product_term_mapping.term_id","cscan_energy.productID as energy","cscan_banking.productID as banking",
                            "cscan_product.affinity_association","cscan_banking.BankingRewardsProgramEmphasis","cscan_banking.BankingRewardsProgram","cscan_payment_cards.*","cscan_energy.ERateType","cscan_energy.EOfferPrice","cscan_energy.ECancelFee","cscan_retail.RCreditCardMentioned","cscan_travel_leisure.productID as travel","cscan_credit_access_checks.productID as credit",
                            "cscan_document_text_search.dts_val","cscan_product.socialmedia_adtype","cscan_product_agent_communication_mapping.agent_communication_id","cscan_telecom.*","cscan_product_language_mapping.language_id","cscan_credit_access_checks.*","cscan_mortgage_loan.productID as mortgage_loan",
                            "cscan_travel_leisure.TLDebitCardMentioned","cscan_travel_leisure.TLCreditCardMentioned","cscan_travel_leisure.TLCardNetwork","cscan_product_state_mapping.state_id","cscan_ancillary_product_mapping.ancillary_product_id","cscan_telecom.productID as telecom","cscan_payment_cards.productID as payment",
                            "cscan_product.added_to_database","cscan_panelists_product.panelist_id","cscan_panelists_product.ppeve","cscan_product_age_mapping.age_id","cscan_product_incometype_mapping.income_id","cscan_product_affinity_mapping.afinity_id","cscan_product_riders_mapping.rider_id",
                            "cscan_panelists_product.ppage","cscan_panelists_product.ppdate","cscan_mortgage_loan.*","cscan_panelists_product.fico_range_id","cscan_panelists_product.vantage_range_id","cscan_panelists_product.creditVision_range_id","cscan_product_groupsize_mapping.group_size_id"
                            ,"cscan_product_electronic_mapping.electronic_id"]
            table_names = ["cscan_product", "cscan_product_agent_communication_mapping","cscan_document_text_search","cscan_banking","cscan_payment_cards","cscan_energy",
                        "cscan_retail","cscan_telecom","cscan_travel_leisure","cscan_product_state_mapping","cscan_panelists_product","cscan_product_age_mapping","cscan_product_incometype_mapping","cscan_product_language_mapping",
                        "cscan_ancillary_product_mapping","cscan_mortgage_loan","cscan_product_groupsize_mapping","cscan_product_affinity_mapping","cscan_product_face_mapping",
                        "cscan_product_riders_mapping","cscan_product_term_mapping","cscan_credit_access_checks","cscan_product_electronic_mapping"]
            join_condition=["cscan_product.product_id=cscan_product_agent_communication_mapping.product_id","cscan_product.product_id=cscan_document_text_search.productID",
                            "cscan_product.product_id=cscan_banking.productID","cscan_product.product_id=cscan_payment_cards.productID"
                            ,"cscan_product.product_id=cscan_energy.productID","cscan_product.product_id=cscan_retail.productID","cscan_product.product_id=cscan_telecom.productID"
                            ,"cscan_product.product_id=cscan_travel_leisure.productID","cscan_product.product_id=cscan_product_state_mapping.product_id","cscan_product.product_id=cscan_panelists_product.productID",
                            "cscan_product.product_id=cscan_product_age_mapping.product_id","cscan_product.product_id=cscan_product_incometype_mapping.product_id","cscan_product.product_id = cscan_product_language_mapping.product_id",
                            "cscan_product.product_id=cscan_ancillary_product_mapping.product_id","cscan_product.product_id=cscan_mortgage_loan.productID","cscan_product.product_id=cscan_product_groupsize_mapping.product_id"
                            ,"cscan_product.product_id=cscan_product_affinity_mapping.product_id","cscan_product.product_id=cscan_product_face_mapping.product_id","cscan_product.product_id=cscan_product_riders_mapping.product_id"
                            ,"cscan_product.product_id=cscan_product_term_mapping.product_id","cscan_product.product_id=cscan_credit_access_checks.productID","cscan_product.product_id=cscan_product_electronic_mapping.product_id"
                            ]
            # Fetch data from the 'product_conn' database
            csv2_product_data = await self.db_query.join_tables(fields_list, table_names, join_condition,product_conn,"cscan_product.product_id",product_id)
            csv2_dataframe = pd.DataFrame(csv2_product_data).fillna(0)
            if "afinity_id" in csv2_dataframe.columns and csv2_dataframe['afinity_id'].unique().any() != 0:
                afinity=await self.model.get_affinity_category(master_lookup_conn,csv2_dataframe) 
                affinity_category_names = [item['AffinityCategoryName'] for item in afinity if item['AffinityCategoryName']]
                print("affinity_category_names",affinity_category_names)
                affinity_names = [item['affinityName'] for item in afinity]   
                combined_affinity_category_name=",".join(affinity_category_names) if affinity_category_names else ""
                combined_affinity_name = ",".join(affinity_names)  
                rawdata.update({
                    "AffinityCategoryName": combined_affinity_category_name,
                    "affinityName": combined_affinity_name
                }) 
            if 'panelist_id' in csv2_dataframe.columns and  csv2_dataframe['panelist_id'].unique().any() != 0:
                panalist=await self.model.get_panalist_eve(master_lookup_conn,csv2_dataframe)     
                rawdata['panelist_details']=panalist
                edc=await self.model.getedc(csv2_dataframe,master_lookup_conn)
                rawdata['edc_name'] = edc
            rawdata['electronic_id']=int(csv2_dataframe['electronic_id'].unique()[0])    
            return csv2_dataframe ,rawdata 
        except Exception as e:
            self.logger.exception(e)
            

class PrintProcessor(MediaProcessor):
    async def process(self, product_conn,master_lookup_conn,product_id,mobile_digital_conn):
        # Implementation specific to Online Display processing
        try:
            rawdata={}
            fields_list=["cscan_product.approved_date","cscan_product.fee_product","cscan_product.prescription","cscan_product.offer_origin_id","cscan_product.is_prescreen","cscan_product.worksite_voluntary","cscan_product.mtype_id","cscan_product.mpanel_id","cscan_product.product_headline","cscan_product.entry_id","cscan_product.is_qa","cscan_banking.DebitCardMentioned","cscan_banking.BankingCardType As Card_id",
                            "cscan_product.affinity_association","cscan_banking.BankingRewardsProgramEmphasis","cscan_banking.BankingRewardsProgram","cscan_banking.MinimumDeposit","cscan_payment_cards.*","cscan_energy.ERateType","cscan_energy.EOfferPrice","cscan_energy.ECancelFee","cscan_retail.RCreditCardMentioned","cscan_product_term_mapping.term_id","cscan_banking.productID as banking",
                            "cscan_document_text_search.dts_val","cscan_product.socialmedia_adtype","cscan_product_agent_communication_mapping.agent_communication_id","cscan_telecom.*","cscan_product_language_mapping.language_id","cscan_product_face_mapping.face_id","cscan_mortgage_loan.productID as mortgage_loan","cscan_payment_cards.productID as payment",
                            "cscan_travel_leisure.TLDebitCardMentioned","cscan_travel_leisure.TLCreditCardMentioned","cscan_travel_leisure.TLCardNetwork","cscan_ancillary_product_mapping.ancillary_product_id","cscan_credit_access_checks.*","cscan_energy.productID as energy","cscan_credit_access_checks.productID as credit",
                            "cscan_product.added_to_database","cscan_product_publication_mapping.publication_id","cscan_product_riders_mapping.rider_id","cscan_telecom.productID as telecom","cscan_travel_leisure.productID as travel",
                            "cscan_product_publication_mapping.publication_date","cscan_mortgage_loan.*","cscan_product_groupsize_mapping.group_size_id","cscan_product_affinity_mapping.afinity_id"]
            table_names = ["cscan_product", "cscan_product_agent_communication_mapping","cscan_document_text_search","cscan_banking","cscan_payment_cards","cscan_energy",
                        "cscan_retail","cscan_telecom","cscan_travel_leisure","cscan_product_language_mapping",
                        "cscan_ancillary_product_mapping","cscan_product_publication_mapping","cscan_mortgage_loan","cscan_product_groupsize_mapping","cscan_product_affinity_mapping","cscan_product_face_mapping",
                        "cscan_product_riders_mapping","cscan_product_term_mapping","cscan_credit_access_checks"]
            join_condition=["cscan_product.product_id=cscan_product_agent_communication_mapping.product_id","cscan_product.product_id=cscan_document_text_search.productID",
                            "cscan_product.product_id=cscan_banking.productID","cscan_product.product_id=cscan_payment_cards.productID"
                            ,"cscan_product.product_id=cscan_energy.productID","cscan_product.product_id=cscan_retail.productID","cscan_product.product_id=cscan_telecom.productID"
                            ,"cscan_product.product_id=cscan_travel_leisure.productID",
                            "cscan_product.product_id = cscan_product_language_mapping.product_id","cscan_product.product_id=cscan_ancillary_product_mapping.product_id",
                            "cscan_product.product_id=cscan_product_publication_mapping.product_id","cscan_product.product_id=cscan_mortgage_loan.productID","cscan_product.product_id=cscan_product_groupsize_mapping.product_id"
                            ,"cscan_product.product_id=cscan_product_affinity_mapping.product_id","cscan_product.product_id=cscan_product_face_mapping.product_id","cscan_product.product_id=cscan_product_riders_mapping.product_id"
                            ,"cscan_product.product_id=cscan_product_term_mapping.product_id","cscan_product.product_id=cscan_credit_access_checks.productID"
                            ]

            # Fetch data from the 'product_conn' database
            csv2_product_data = await self.db_query.join_tables(fields_list, table_names, join_condition,product_conn,"cscan_product.product_id",product_id)
            csv2_dataframe = pd.DataFrame(csv2_product_data).fillna(0)
            if "afinity_id" in csv2_dataframe.columns and csv2_dataframe['afinity_id'].unique().any() != 0:
                afinity=await self.model.get_affinity_category(master_lookup_conn,csv2_dataframe) 
                affinity_category_names = [item['AffinityCategoryName'] for item in afinity if item['AffinityCategoryName']]
                print("affinity_category_names",affinity_category_names)
                affinity_names = [item['affinityName'] for item in afinity]   
                combined_affinity_category_name=",".join(affinity_category_names) if affinity_category_names else ""
                combined_affinity_name = ",".join(affinity_names)  
                rawdata.update({
                    "AffinityCategoryName": combined_affinity_category_name,
                    "affinityName": combined_affinity_name
                })
            if not csv2_dataframe['publication_date'].isin([0]).all():     
                csv2_dataframe['publication_date'] = csv2_dataframe['publication_date'].astype("str")
                if 'publication_id' in csv2_dataframe :
                    print_data=await self.model.get_print_type(master_lookup_conn,csv2_dataframe)
                    print_dataframe=pd.DataFrame(print_data)
                    print_dataframe['publication_date']=csv2_dataframe["publication_date"]
                    rawdata['print_details']=print_dataframe.drop(columns=['publicationID']).to_dict(orient='records')
            return csv2_dataframe ,rawdata 
        except Exception as e:
            self.logger.exception(e)
            

class DashboardProcessor(MediaProcessor):
    async def process(self, product_conn,master_lookup_conn,product_id,mobile_digital_conn):
        # Implementation specific to Dashboard processing
        try:
            rawdata={}
            fields_list=["cscan_product.approved_date","cscan_product.fee_product","cscan_product.prescription","cscan_product.offer_origin_id","cscan_product.is_prescreen","cscan_product.worksite_voluntary","cscan_product.mtype_id","cscan_product.mpanel_id","cscan_product.product_headline","cscan_product.entry_id","cscan_product.is_qa","cscan_banking.DebitCardMentioned","cscan_banking.BankingCardType As Card_id",
                            "cscan_product.affinity_association","cscan_banking.BankingRewardsProgramEmphasis","cscan_banking.BankingRewardsProgram","cscan_banking.MinimumDeposit","cscan_payment_cards.*","cscan_energy.ERateType","cscan_energy.EOfferPrice","cscan_energy.ECancelFee","cscan_retail.RCreditCardMentioned","cscan_product_riders_mapping.rider_id","cscan_energy.productID as energy","cscan_payment_cards.productID as payment",
                            "cscan_document_text_search.dts_val","cscan_product.socialmedia_adtype","cscan_product_agent_communication_mapping.agent_communication_id","cscan_telecom.*","cscan_product_language_mapping.language_id","cscan_product_face_mapping.face_id","cscan_product_term_mapping.term_id","cscan_mortgage_loan.productID as mortgage_loan",
                            "cscan_product.added_to_database","cscan_travel_leisure.TLDebitCardMentioned","cscan_travel_leisure.TLCreditCardMentioned","cscan_travel_leisure.TLCardNetwork","cscan_product_state_mapping.state_id","cscan_ancillary_product_mapping.ancillary_product_id","cscan_credit_access_checks.*","cscan_credit_access_checks.productID as credit",
                            "cscan_product_age_mapping.age_id","cscan_product_incometype_mapping.income_id","cscan_mortgage_loan.*","cscan_product_groupsize_mapping.group_size_id","cscan_product_affinity_mapping.afinity_id","cscan_telecom.productID as telecom","cscan_travel_leisure.productID as travel","cscan_banking.productID as banking"
                            ]
            table_names = ["cscan_product", "cscan_product_agent_communication_mapping","cscan_document_text_search","cscan_banking","cscan_payment_cards","cscan_energy",
                        "cscan_retail","cscan_telecom","cscan_travel_leisure","cscan_product_state_mapping","cscan_product_age_mapping","cscan_product_incometype_mapping","cscan_product_language_mapping",
                        "cscan_ancillary_product_mapping","cscan_mortgage_loan","cscan_product_groupsize_mapping","cscan_product_affinity_mapping","cscan_product_face_mapping","cscan_product_riders_mapping",
                        "cscan_product_term_mapping","cscan_credit_access_checks"]
            join_condition=["cscan_product.product_id=cscan_product_agent_communication_mapping.product_id","cscan_product.product_id=cscan_document_text_search.productID",
                            "cscan_product.product_id=cscan_banking.productID","cscan_product.product_id=cscan_payment_cards.productID"
                            ,"cscan_product.product_id=cscan_energy.productID","cscan_product.product_id=cscan_retail.productID","cscan_product.product_id=cscan_telecom.productID"
                            ,"cscan_product.product_id=cscan_travel_leisure.productID","cscan_product.product_id=cscan_product_state_mapping.product_id",
                            "cscan_product.product_id=cscan_product_age_mapping.product_id","cscan_product.product_id=cscan_product_incometype_mapping.product_id",
                            "cscan_product.product_id = cscan_product_language_mapping.product_id","cscan_product.product_id=cscan_ancillary_product_mapping.product_id"
                            ,"cscan_product.product_id=cscan_mortgage_loan.productID","cscan_product.product_id=cscan_product_groupsize_mapping.product_id"
                            ,"cscan_product.product_id=cscan_product_affinity_mapping.product_id","cscan_product.product_id=cscan_product_face_mapping.product_id","cscan_product.product_id=cscan_product_riders_mapping.product_id"
                            ,"cscan_product.product_id=cscan_product_term_mapping.product_id","cscan_product.product_id=cscan_credit_access_checks.productID"
                            ]
            # Fetch data from the 'product_conn' database
            csv2_product_data = await self.db_query.join_tables(fields_list, table_names, join_condition,product_conn,"cscan_product.product_id",product_id)
            csv2_dataframe = pd.DataFrame(csv2_product_data).fillna(0) 
            return csv2_dataframe ,rawdata 
        except Exception as e:
            self.logger.exception(e)
            

class DirectMailProcessor(MediaProcessor):
    async def process(self, product_conn,master_lookup_conn,product_id,mobile_digital_conn):
        # Implementation specific to Direct Mail processing
        try:
            rawdata={}
            fields_list=["cscan_product.approved_date","cscan_product.fee_product","cscan_product.delivery_method_id","cscan_product.prescription","cscan_product.offer_origin_id","cscan_product.is_prescreen","cscan_product.worksite_voluntary","cscan_product.mtype_id","cscan_product.mpanel_id","cscan_product.product_headline",
                            "cscan_product.entry_id","cscan_product.is_qa","cscan_banking.DebitCardMentioned","cscan_banking.MinimumDeposit","cscan_banking.BankingCardType As Card_id","cscan_product_term_mapping.term_id","cscan_credit_access_checks.*","cscan_travel_leisure.productID as travel","cscan_credit_access_checks.productID as credit",
                            "cscan_product.affinity_association","cscan_banking.BankingRewardsProgramEmphasis","cscan_banking.BankingRewardsProgram","cscan_payment_cards.*","cscan_energy.ERateType","cscan_energy.EOfferPrice","cscan_energy.ECancelFee","cscan_retail.RCreditCardMentioned","cscan_energy.productID as energy","cscan_banking.productID as banking",
                            "cscan_document_text_search.dts_val","cscan_product.socialmedia_adtype","cscan_product_agent_communication_mapping.agent_communication_id","cscan_telecom.productID as telecom","cscan_telecom.*","cscan_product_language_mapping.language_id","cscan_product_face_mapping.face_id","cscan_payment_cards.productID as payment",
                            "cscan_travel_leisure.TLDebitCardMentioned","cscan_travel_leisure.TLCreditCardMentioned","cscan_travel_leisure.TLCardNetwork","cscan_product_state_mapping.state_id","cscan_ancillary_product_mapping.ancillary_product_id","cscan_product_riders_mapping.rider_id",
                            "cscan_product.added_to_database","cscan_panelists_product.panelist_id","cscan_panelists_product.ppmv","cscan_product_age_mapping.age_id","cscan_product_incometype_mapping.income_id","cscan_product_affinity_mapping.afinity_id","cscan_document.document_size_byte","cscan_mortgage_loan.productID as mortgage_loan",
                            "cscan_panelists_product.ppage","cscan_panelists_product.ppdate","cscan_panelists_product.fico_range_id","cscan_panelists_product.vantage_range_id","cscan_panelists_product.creditVision_range_id","cscan_mortgage_loan.*","cscan_product_groupsize_mapping.group_size_id"]
            
            table_names = ["cscan_product", "cscan_product_agent_communication_mapping","cscan_document_text_search","cscan_banking","cscan_payment_cards","cscan_energy",
                        "cscan_retail","cscan_telecom","cscan_travel_leisure","cscan_product_state_mapping"," LATERAL ("
                    "SELECT panelist_id, ppmv, ppage, ppdate, fico_range_id, vantage_range_id, creditVision_range_id "
                    "FROM cscan_panelists_product "
                    "WHERE productID = cscan_product.product_id "
                    "LIMIT 10"
                ") AS cscan_panelists_product","cscan_product_age_mapping","cscan_product_incometype_mapping","cscan_product_language_mapping",
                        "cscan_ancillary_product_mapping","cscan_mortgage_loan","cscan_product_groupsize_mapping","cscan_product_affinity_mapping","cscan_product_face_mapping","cscan_product_riders_mapping","cscan_document",
                        "cscan_product_term_mapping","cscan_credit_access_checks"]
            
            join_condition=["cscan_product.product_id=cscan_product_agent_communication_mapping.product_id","cscan_product.product_id=cscan_document_text_search.productID",
                            "cscan_product.product_id=cscan_banking.productID","cscan_product.product_id=cscan_payment_cards.productID"
                            ,"cscan_product.product_id=cscan_energy.productID","cscan_product.product_id=cscan_retail.productID","cscan_product.product_id=cscan_telecom.productID"
                            ,"cscan_product.product_id=cscan_travel_leisure.productID","cscan_product.product_id=cscan_product_state_mapping.product_id","true",
                            "cscan_product.product_id=cscan_product_age_mapping.product_id","cscan_product.product_id=cscan_product_incometype_mapping.product_id","cscan_product.product_id = cscan_product_language_mapping.product_id",
                            "cscan_product.product_id=cscan_ancillary_product_mapping.product_id","cscan_product.product_id=cscan_mortgage_loan.productID","cscan_product.product_id = cscan_product_groupsize_mapping.product_id"
                            ,"cscan_product.product_id=cscan_product_affinity_mapping.product_id","cscan_product.product_id=cscan_product_face_mapping.product_id","cscan_product.product_id=cscan_product_riders_mapping.product_id"
                            ,"cscan_product.product_id=cscan_document.productID","cscan_product.product_id=cscan_product_term_mapping.product_id","cscan_product.product_id=cscan_credit_access_checks.productID"
                            ]         
            #Fetch data from the 'product_conn' database
            csv2_product_data = await self.db_query.join_tables(fields_list, table_names, join_condition,product_conn,"cscan_product.product_id",product_id)
            csv2_dataframe = pd.DataFrame(csv2_product_data).fillna(0) 
            mobile_fields_list=["cscan_digital_observation.simple_domain"]
            mobile_table_names=["cscan_digital_creative","cscan_semdetails","cscan_digital_observation"]
            mobile_join_conditions=["cscan_digital_creative.ad_md5=cscan_semdetails.ad_md5","cscan_digital_creative.ad_md5=cscan_digital_observation.ad_md5"]
            mobile_digital = await self.db_query.join_tables(mobile_fields_list, mobile_table_names, mobile_join_conditions,mobile_digital_conn,"cscan_digital_creative.productID",product_id)
            self.logger.info(f"mobile_digital{mobile_digital}")
            mobile_digital_dataframe = pd.DataFrame(mobile_digital)
            if not mobile_digital_dataframe.empty:
                self.logger.info(f"mobile_digital_dataframe{mobile_digital_dataframe}")
                mobile_digital_json = mobile_digital_dataframe.to_dict(orient='records')
                for record in mobile_digital_json:
                    rawdata.update(record)
                    self.logger.info(f"Updated rawdata with mobile digital record{rawdata}")
                # estimatespend=await self.model.getpanalist_spendImpression(product_id,master_lookup_conn,mobile_digital_conn)
                # rawdata["panelists_spend_impression"]=estimatespend       
            else:
                self.logger.info("No mobile digital JSON data provided.")
                pass   
            
            if "afinity_id" in csv2_dataframe.columns and csv2_dataframe['afinity_id'].unique().any() != 0:
                afinity=await self.model.get_affinity_category(master_lookup_conn,csv2_dataframe) 
                affinity_category_names = [item['AffinityCategoryName'] for item in afinity if item['AffinityCategoryName']]
                print("affinity_category_names",affinity_category_names)
                affinity_names = [item['affinityName'] for item in afinity]   
                combined_affinity_category_name=",".join(affinity_category_names) if affinity_category_names else ""
                combined_affinity_name = ",".join(affinity_names)  
                self.logger.info(f"AffinityCategoryName combined: {combined_affinity_category_name}")
                self.logger.info(f"AffinityName combined: {combined_affinity_name}")

                rawdata.update({
                    "AffinityCategoryName": combined_affinity_category_name,
                    "affinityName": combined_affinity_name
                })
            else:
                self.logger.info("No valid 'afinity_id' column data found in csv2_dataframe.")
         
                #rawdata.update(afinity[0])
                
            if 'panelist_id' in csv2_dataframe and csv2_dataframe['panelist_id'].unique().any() != 0:
                self.logger.info("Processing panelist details.")
                panalist=await self.model.get_panalist_eve(master_lookup_conn,csv2_dataframe)     
                rawdata['panelist_details']=panalist
                edc=await self.model.getedc(csv2_dataframe,master_lookup_conn)
                rawdata['edc_name'] = edc
                self.logger.info("Panelist details and edc_name updated in rawdata.")
            else:
                self.logger.info("No valid 'panelist_id' column data found in csv2_dataframe.")
    
            if 'document_size_byte' in csv2_dataframe and csv2_dataframe['document_size_byte'].unique().any() != 0:
                self.logger.info("Processing document size for estimate spend calculation.")
                panelist_data = rawdata.get("panelist_details")
                if panelist_data:
                    last_panelist_detail = rawdata['panelist_details'][-1]
                    total_ppmv = last_panelist_detail.get('total_ppmv')
                    rawdata['estimate_spend']=await self.model.doSpend(total_ppmv,csv2_dataframe['document_size_byte'].unique()[0])
                    if rawdata['estimate_spend'] is not None:
                        rawdata['estimate_spend'] = round(rawdata['estimate_spend'])
                        self.logger.info(f"Calculated and rounded estimate spend: {rawdata['estimate_spend']}")
                    else:
                        self.logger.info("Estimate spend calculation returned None.")  
                else:
                    self.logger.info("No panelist details available for spend estimation.") 
            else:
                self.logger.info("No valid 'document_size_byte' column data found in csv2_dataframe.")
                 
            return csv2_dataframe ,rawdata     
        except Exception as e:
            self.logger.exception(e)
            
        
class SearchEngineMarketingProcessor(MediaProcessor):
    async def process(self, product_conn,master_lookup_conn,product_id,mobile_digital_conn):
        # Implementation specific to Online Display processing
        # Implementation specific to Online Display processing
        rawdata={}
        fields_list=["cscan_product.approved_date","cscan_product.is_mobile","cscan_product.fee_product","cscan_product.prescription","cscan_product.offer_origin_id","cscan_product.is_prescreen","cscan_product.worksite_voluntary","cscan_product.mtype_id","cscan_product.mpanel_id","cscan_product.product_headline","cscan_product.entry_id","cscan_product.is_qa","cscan_banking.DebitCardMentioned","cscan_banking.BankingCardType As Card_id",
                        "cscan_product.affinity_association","cscan_banking.BankingRewardsProgramEmphasis","cscan_banking.BankingRewardsProgram","cscan_banking.MinimumDeposit","cscan_payment_cards.*","cscan_energy.ERateType","cscan_energy.EOfferPrice","cscan_energy.ECancelFee","cscan_retail.RCreditCardMentioned","cscan_product_riders_mapping.rider_id","cscan_product_term_mapping.term_id","cscan_credit_access_checks.productID as credit",
                        "cscan_document_text_search.dts_val","cscan_product.socialmedia_adtype","cscan_product_agent_communication_mapping.agent_communication_id","cscan_telecom.*","cscan_product_language_mapping.language_id","cscan_product_face_mapping.face_id","cscan_panelists_product.panelist_id","cscan_mortgage_loan.productID as mortgage_loan",
                        "cscan_product.added_to_database","cscan_travel_leisure.TLDebitCardMentioned","cscan_travel_leisure.TLCreditCardMentioned","cscan_travel_leisure.TLCardNetwork","cscan_product_state_mapping.state_id","cscan_ancillary_product_mapping.ancillary_product_id","cscan_credit_access_checks.*","cscan_energy.productID as energy","cscan_payment_cards.productID as payment",
                        "cscan_product_age_mapping.age_id","cscan_product_incometype_mapping.income_id","cscan_mortgage_loan.*","cscan_product_groupsize_mapping.group_size_id","cscan_product_affinity_mapping.afinity_id","cscan_telecom.productID as telecom","cscan_travel_leisure.productID as travel","cscan_banking.productID as banking"
                       ]
        table_names = ["cscan_product", "cscan_product_agent_communication_mapping","cscan_document_text_search","cscan_banking","cscan_payment_cards","cscan_energy",
                       "cscan_retail","cscan_telecom","cscan_travel_leisure","cscan_product_state_mapping","cscan_product_age_mapping","cscan_product_incometype_mapping","cscan_product_language_mapping",
                       "cscan_ancillary_product_mapping","cscan_mortgage_loan","cscan_product_groupsize_mapping","cscan_product_affinity_mapping","cscan_product_face_mapping","cscan_product_riders_mapping"
                       ,"cscan_panelists_product","cscan_product_term_mapping","cscan_credit_access_checks"]
        join_condition=["cscan_product.product_id=cscan_product_agent_communication_mapping.product_id","cscan_product.product_id=cscan_document_text_search.productID",
                        "cscan_product.product_id=cscan_banking.productID","cscan_product.product_id=cscan_payment_cards.productID"
                        ,"cscan_product.product_id=cscan_energy.productID","cscan_product.product_id=cscan_retail.productID","cscan_product.product_id=cscan_telecom.productID"
                        ,"cscan_product.product_id=cscan_travel_leisure.productID","cscan_product.product_id=cscan_product_state_mapping.product_id",
                        "cscan_product.product_id=cscan_product_age_mapping.product_id","cscan_product.product_id=cscan_product_incometype_mapping.product_id",
                        "cscan_product.product_id = cscan_product_language_mapping.product_id","cscan_product.product_id=cscan_ancillary_product_mapping.product_id"
                        ,"cscan_product.product_id=cscan_mortgage_loan.productID","cscan_product.product_id=cscan_product_groupsize_mapping.product_id"
                        ,"cscan_product.product_id=cscan_product_affinity_mapping.product_id","cscan_product.product_id=cscan_product_face_mapping.product_id"
                        ,"cscan_product.product_id=cscan_product_riders_mapping.product_id","cscan_product.product_id=cscan_panelists_product.productID"
                        ,"cscan_product.product_id=cscan_product_term_mapping.product_id","cscan_product.product_id=cscan_credit_access_checks.productID"
                        ]
        # Fetch data from the 'product_conn' database
        csv2_product_data = await self.db_query.join_tables(fields_list, table_names, join_condition,product_conn,"cscan_product.product_id",product_id)
        csv2_dataframe = pd.DataFrame(csv2_product_data).fillna(0) 
        mobile_fields_list=["cscan_semdetails.sem_headline","cscan_semdetails.sem_url","cscan_semdetails.sem_description","cscan_semdetails.sem_search_key","cscan_digital_observation.simple_domain"]
        mobile_table_names=["cscan_digital_creative","cscan_semdetails","cscan_digital_observation"]
        mobile_join_conditions=["cscan_digital_creative.ad_md5=cscan_semdetails.ad_md5","cscan_digital_creative.ad_md5=cscan_digital_observation.ad_md5"]
        mobile_digital = await self.db_query.join_tables(mobile_fields_list, mobile_table_names, mobile_join_conditions,mobile_digital_conn,"cscan_digital_creative.productID",product_id)
        mobile_digital_dataframe = pd.DataFrame(mobile_digital)
        if not mobile_digital_dataframe.empty:
            mobile_digital_json = mobile_digital_dataframe.to_dict(orient='records')
            for record in mobile_digital_json:
                rawdata.update(record)      
        else:
            pass
        estimatespend=await self.model.getpanalist_spendImpression(product_id,master_lookup_conn,mobile_digital_conn)
        if estimatespend:
            rawdata["panelists_spend_impression"]=estimatespend 
        if 'is_mobile': 
            rawdata["is_mobile"]=int(csv2_dataframe['is_mobile'].unique()[0])
        return csv2_dataframe ,rawdata 

class OnlineDisplayProcessor(MediaProcessor):
    async def process(self, product_conn,master_lookup_conn,product_id,mobile_digital_conn):
        # Implementation specific to Online Display processing
        try:
            rawdata={}
            fields_list=["cscan_product.approved_date","cscan_product.fee_product","cscan_product.prescription","cscan_product.offer_origin_id","cscan_product.is_prescreen","cscan_product.worksite_voluntary","cscan_product.mtype_id","cscan_product.mpanel_id","cscan_product.product_headline","cscan_product.entry_id","cscan_product.is_qa","cscan_banking.DebitCardMentioned","cscan_banking.BankingCardType As Card_id",
                            "cscan_product.affinity_association","cscan_banking.BankingRewardsProgramEmphasis","cscan_banking.BankingRewardsProgram","cscan_banking.MinimumDeposit","cscan_payment_cards.*","cscan_energy.ERateType","cscan_energy.EOfferPrice","cscan_energy.ECancelFee","cscan_retail.RCreditCardMentioned","cscan_product_face_mapping.face_id","cscan_mortgage_loan.productID as mortgage_loan","cscan_payment_cards.productID as payment",
                            "cscan_document_text_search.dts_val","cscan_product.socialmedia_adtype","cscan_product_agent_communication_mapping.agent_communication_id","cscan_telecom.*","cscan_product_language_mapping.language_id","cscan_product_riders_mapping.rider_id","cscan_product_term_mapping.term_id","cscan_credit_access_checks.*","cscan_travel_leisure.productID as travel",
                            "cscan_product.added_to_database","cscan_travel_leisure.TLDebitCardMentioned","cscan_travel_leisure.TLCreditCardMentioned","cscan_travel_leisure.TLCardNetwork","cscan_product_state_mapping.state_id","cscan_ancillary_product_mapping.ancillary_product_id","cscan_document.document_placement","cscan_energy.productID as energy","cscan_credit_access_checks.productID as credit",
                            "cscan_product_age_mapping.age_id","cscan_product_incometype_mapping.income_id","cscan_document.document_content_type","cscan_mortgage_loan.*","cscan_product_groupsize_mapping.group_size_id","cscan_product_affinity_mapping.afinity_id","cscan_product_info.traffic_sources","cscan_telecom.productID as telecom","cscan_banking.productID as banking"
                            ]
            table_names = ["cscan_product", "cscan_product_agent_communication_mapping","cscan_document_text_search","cscan_banking","cscan_payment_cards","cscan_energy",
                        "cscan_retail","cscan_telecom","cscan_travel_leisure","cscan_product_state_mapping","cscan_product_age_mapping","cscan_product_incometype_mapping","cscan_product_language_mapping",
                        "cscan_ancillary_product_mapping","cscan_document","cscan_mortgage_loan","cscan_product_groupsize_mapping","cscan_product_affinity_mapping","cscan_product_face_mapping",
                        "cscan_product_info","cscan_product_riders_mapping","cscan_product_term_mapping","cscan_credit_access_checks"]
            join_condition=["cscan_product.product_id=cscan_product_agent_communication_mapping.product_id","cscan_product.product_id=cscan_document_text_search.productID",
                            "cscan_product.product_id=cscan_banking.productID","cscan_product.product_id=cscan_payment_cards.productID"
                            ,"cscan_product.product_id=cscan_energy.productID","cscan_product.product_id=cscan_retail.productID","cscan_product.product_id=cscan_telecom.productID"
                            ,"cscan_product.product_id=cscan_travel_leisure.productID","cscan_product.product_id=cscan_product_state_mapping.product_id",
                            "cscan_product.product_id=cscan_product_age_mapping.product_id","cscan_product.product_id=cscan_product_incometype_mapping.product_id","cscan_product.product_id = cscan_product_language_mapping.product_id",
                            "cscan_product.product_id=cscan_ancillary_product_mapping.product_id","cscan_product.product_id=cscan_document.productID","cscan_product.product_id=cscan_mortgage_loan.productID"
                            ,"cscan_product.product_id = cscan_product_groupsize_mapping.product_id","cscan_product.product_id=cscan_product_affinity_mapping.product_id","cscan_product.product_id=cscan_product_face_mapping.product_id"
                            ,"cscan_product.product_id=cscan_product_info.product_id","cscan_product.product_id=cscan_product_riders_mapping.product_id"
                            ,"cscan_product.product_id=cscan_product_term_mapping.product_id","cscan_product.product_id=cscan_credit_access_checks.productID"
                            ]
            # Fetch data from the 'product_conn' database
            csv2_product_data = await self.db_query.join_tables(fields_list, table_names, join_condition,product_conn,"cscan_product.product_id",product_id)
            csv2_dataframe = pd.DataFrame(csv2_product_data).fillna(0) 
        
            mobile_fields_list=["cscan_digital_observation.simple_domain"]
            mobile_table_names=["cscan_digital_creative","cscan_semdetails","cscan_digital_observation"]
            mobile_join_conditions=["cscan_digital_creative.ad_md5=cscan_semdetails.ad_md5","cscan_digital_creative.ad_md5=cscan_digital_observation.ad_md5"]
            mobile_digital = await self.db_query.join_tables(mobile_fields_list, mobile_table_names, mobile_join_conditions,mobile_digital_conn,"cscan_digital_creative.productID",product_id)
            mobile_digital_dataframe = pd.DataFrame(mobile_digital)
            # print("mobile_digital_dataframe==",mobile_digital_dataframe)
            if not mobile_digital_dataframe.empty:
                mobile_digital_json = mobile_digital_dataframe.to_dict(orient='records')
                for record in mobile_digital_json:
                    rawdata.update(record)            
            else:
                pass  
            estimatespend=await self.model.getpanalist_spendImpression(product_id,master_lookup_conn,mobile_digital_conn)
            rawdata["panelists_spend_impression"]=estimatespend  
            if "document_content_type" in csv2_dataframe.columns and csv2_dataframe['document_content_type'].unique().any() != 0:         
                doc=await self.model.get_cscan_document(csv2_dataframe) 
                rawdata.update(doc)
            if "traffic_sources" in csv2_dataframe.columns and  csv2_dataframe['traffic_sources'].unique().any() != 0:  
                rawdata['traffic_sources']=csv2_dataframe['traffic_sources'].unique()[0]
                rawdata["traffic_site_count"]=floor(len(csv2_dataframe['traffic_sources'].unique()) / 56) + 1        
            return csv2_dataframe ,rawdata 
        except Exception as e:
            self.logger.exception(e)
            

class OnlineVideoProcessor(MediaProcessor):
    async def process(self, product_conn,master_lookup_conn,product_id,mobile_digital_conn):
        # Implementation specific to Online Display processing
        try:
            rawdata={}
            fields_list=["cscan_product.approved_date","cscan_product.is_mobile","cscan_product.fee_product","cscan_product.prescription","cscan_product.offer_origin_id","cscan_product.is_prescreen","cscan_product.worksite_voluntary","cscan_product.mtype_id","cscan_product.mpanel_id","cscan_product.product_headline","cscan_product.entry_id","cscan_product.is_qa","cscan_banking.DebitCardMentioned","cscan_banking.BankingCardType As Card_id",
                            "cscan_product.affinity_association","cscan_banking.BankingRewardsProgramEmphasis","cscan_banking.BankingRewardsProgram","cscan_banking.MinimumDeposit","cscan_payment_cards.*","cscan_energy.ERateType","cscan_energy.EOfferPrice","cscan_energy.ECancelFee","cscan_retail.RCreditCardMentioned","cscan_product_riders_mapping.rider_id","cscan_mortgage_loan.productID as mortgage_loan","cscan_credit_access_checks.productID as credit",
                            "cscan_document_text_search.dts_val","cscan_product.socialmedia_adtype","cscan_product_agent_communication_mapping.agent_communication_id","cscan_telecom.*","cscan_product_language_mapping.language_id","cscan_product_face_mapping.face_id","cscan_credit_access_checks.*","cscan_energy.productID as energy","cscan_travel_leisure.productID as travel","cscan_payment_cards.productID as payment",
                            "cscan_travel_leisure.TLDebitCardMentioned","cscan_travel_leisure.TLCreditCardMentioned","cscan_travel_leisure.TLCardNetwork","cscan_product_state_mapping.state_id","cscan_ancillary_product_mapping.ancillary_product_id","cscan_product_term_mapping.term_id","cscan_telecom.productID as telecom","cscan_banking.productID as banking",
                            "cscan_product.added_to_database","cscan_product_age_mapping.age_id","cscan_product_incometype_mapping.income_id","cscan_mortgage_loan.*","cscan_product_groupsize_mapping.group_size_id","cscan_product_affinity_mapping.afinity_id"
                            ]
            table_names = ["cscan_product", "cscan_product_agent_communication_mapping","cscan_document_text_search","cscan_banking","cscan_payment_cards","cscan_energy",
                        "cscan_retail","cscan_telecom","cscan_travel_leisure","cscan_product_state_mapping","cscan_product_age_mapping","cscan_product_incometype_mapping","cscan_product_language_mapping",
                        "cscan_ancillary_product_mapping","cscan_mortgage_loan","cscan_product_groupsize_mapping","cscan_product_affinity_mapping","cscan_product_face_mapping","cscan_product_riders_mapping"
                        ,"cscan_product_term_mapping","cscan_credit_access_checks"]
            join_condition=["cscan_product.product_id=cscan_product_agent_communication_mapping.product_id","cscan_product.product_id=cscan_document_text_search.productID",
                            "cscan_product.product_id=cscan_banking.productID","cscan_product.product_id=cscan_payment_cards.productID"
                            ,"cscan_product.product_id=cscan_energy.productID","cscan_product.product_id=cscan_retail.productID","cscan_product.product_id=cscan_telecom.productID"
                            ,"cscan_product.product_id=cscan_travel_leisure.productID","cscan_product.product_id=cscan_product_state_mapping.product_id",
                            "cscan_product.product_id=cscan_product_age_mapping.product_id","cscan_product.product_id=cscan_product_incometype_mapping.product_id","cscan_product.product_id = cscan_product_language_mapping.product_id",
                            "cscan_product.product_id=cscan_ancillary_product_mapping.product_id","cscan_product.product_id=cscan_mortgage_loan.productID","cscan_product.product_id = cscan_product_groupsize_mapping.product_id"
                            ,"cscan_product.product_id=cscan_product_affinity_mapping.product_id","cscan_product.product_id=cscan_product_face_mapping.product_id","cscan_product.product_id=cscan_product_riders_mapping.product_id"
                            ,"cscan_product.product_id=cscan_product_term_mapping.product_id","cscan_product.product_id=cscan_credit_access_checks.productID"
                            ]
            # Fetch data from the 'product_conn' database
            csv2_product_data = await self.db_query.join_tables(fields_list, table_names, join_condition,product_conn,"cscan_product.product_id",product_id)
            csv2_dataframe = pd.DataFrame(csv2_product_data).fillna(0)
            if "afinity_id" in csv2_dataframe.columns and csv2_dataframe['afinity_id'].unique().any() != 0:
                afinity=await self.model.get_affinity_category(master_lookup_conn,csv2_dataframe) 
                affinity_category_names = [item['AffinityCategoryName'] for item in afinity if item['AffinityCategoryName']]
                print("affinity_category_names",affinity_category_names)
                affinity_names = [item['affinityName'] for item in afinity]   
                combined_affinity_category_name=",".join(affinity_category_names) if affinity_category_names else ""
                combined_affinity_name = ",".join(affinity_names)  
                rawdata.update({
                    "AffinityCategoryName": combined_affinity_category_name,
                    "affinityName": combined_affinity_name
                })
                #rawdata.update(afinity[0])
                
            mobile_fields_list=["cscan_digital_observation.simple_domain"]
            mobile_table_names=["cscan_digital_creative","cscan_semdetails","cscan_digital_observation"]
            mobile_join_conditions=["cscan_digital_creative.ad_md5=cscan_semdetails.ad_md5","cscan_digital_creative.ad_md5=cscan_digital_observation.ad_md5"]
            mobile_digital = await self.db_query.join_tables(mobile_fields_list, mobile_table_names, mobile_join_conditions,mobile_digital_conn,"cscan_digital_creative.productID",product_id)
            mobile_digital_dataframe = pd.DataFrame(mobile_digital)
            if not mobile_digital_dataframe.empty:
                mobile_digital_json = mobile_digital_dataframe.to_dict(orient='records')
                for record in mobile_digital_json:
                    rawdata.update(record)      
            else:
                pass
            estimatespend=await self.model.getpanalist_spendImpression(product_id,master_lookup_conn,mobile_digital_conn)
            rawdata["panelists_spend_impression"]=estimatespend 
            if 'is_mobile' in csv2_dataframe:
                rawdata["is_mobile"]=int(csv2_dataframe['is_mobile'].unique()[0])
            
            return csv2_dataframe ,rawdata 
        except Exception as e:
            self.logger.exception(e)
            

class Website_URLProcessor(MediaProcessor):
    async def process(self, product_conn,master_lookup_conn,product_id,mobile_digital_conn):
        # Implementation specific to Online Display processing
        try:
            rawdata={}
            fields_list=["cscan_product.approved_date","cscan_product.fee_product","cscan_product.prescription","cscan_product.offer_origin_id","cscan_product.is_prescreen","cscan_product.worksite_voluntary","cscan_product.mtype_id","cscan_product.mpanel_id","cscan_product.product_headline","cscan_product.entry_id","cscan_product.is_qa","cscan_banking.DebitCardMentioned","cscan_banking.BankingCardType As Card_id",
                            "cscan_product.affinity_association","cscan_banking.BankingRewardsProgramEmphasis","cscan_banking.BankingRewardsProgram","cscan_banking.MinimumDeposit","cscan_payment_cards.*","cscan_energy.ERateType","cscan_energy.EOfferPrice","cscan_energy.ECancelFee","cscan_retail.RCreditCardMentioned","cscan_document.document_content_type","cscan_mortgage_loan.productID as mortgage_loan","cscan_banking.productID as banking",
                            "cscan_document_text_search.dts_val","cscan_product.socialmedia_adtype","cscan_product_agent_communication_mapping.agent_communication_id","cscan_telecom.*","cscan_product_language_mapping.language_id","cscan_product_face_mapping.face_id","cscan_product_riders_mapping.rider_id","cscan_credit_access_checks.*","cscan_travel_leisure.productID as travel","cscan_payment_cards.productID as payment",
                            "cscan_travel_leisure.TLDebitCardMentioned","cscan_travel_leisure.TLCreditCardMentioned","cscan_travel_leisure.TLCardNetwork","cscan_product_state_mapping.state_id","cscan_ancillary_product_mapping.ancillary_product_id","cscan_document.document_placement","cscan_product_term_mapping.term_id","cscan_energy.productID as energy","cscan_credit_access_checks.productID as credit",
                            "cscan_product.added_to_database","cscan_product_age_mapping.age_id","cscan_product_incometype_mapping.income_id","cscan_mortgage_loan.*","cscan_product_groupsize_mapping.group_size_id","cscan_product_affinity_mapping.afinity_id","cscan_product_info.traffic_sources","cscan_telecom.productID as telecom"
                            ]
            table_names = ["cscan_product", "cscan_product_agent_communication_mapping","cscan_document_text_search","cscan_banking","cscan_payment_cards","cscan_energy",
                        "cscan_retail","cscan_telecom","cscan_travel_leisure","cscan_product_state_mapping","cscan_product_age_mapping","cscan_product_incometype_mapping","cscan_product_language_mapping",
                        "cscan_ancillary_product_mapping","cscan_mortgage_loan","cscan_product_groupsize_mapping","cscan_product_affinity_mapping","cscan_product_face_mapping","cscan_document",
                        "cscan_product_riders_mapping","cscan_product_info","cscan_product_term_mapping","cscan_credit_access_checks"]
            join_condition=["cscan_product.product_id=cscan_product_agent_communication_mapping.product_id","cscan_product.product_id=cscan_document_text_search.productID",
                            "cscan_product.product_id=cscan_banking.productID","cscan_product.product_id=cscan_payment_cards.productID"
                            ,"cscan_product.product_id=cscan_energy.productID","cscan_product.product_id=cscan_retail.productID","cscan_product.product_id=cscan_telecom.productID"
                            ,"cscan_product.product_id=cscan_travel_leisure.productID","cscan_product.product_id=cscan_product_state_mapping.product_id",
                            "cscan_product.product_id=cscan_product_age_mapping.product_id","cscan_product.product_id=cscan_product_incometype_mapping.product_id","cscan_product.product_id = cscan_product_language_mapping.product_id",
                            "cscan_product.product_id=cscan_ancillary_product_mapping.product_id","cscan_product.product_id=cscan_mortgage_loan.productID","cscan_product.product_id = cscan_product_groupsize_mapping.product_id"
                            ,"cscan_product.product_id=cscan_product_affinity_mapping.product_id","cscan_product.product_id=cscan_product_face_mapping.product_id","cscan_product.product_id=cscan_document.productID"
                            ,"cscan_product.product_id=cscan_product_riders_mapping.product_id","cscan_product.product_id=cscan_product_info.product_id"
                            ,"cscan_product.product_id=cscan_product_term_mapping.product_id","cscan_product.product_id=cscan_credit_access_checks.productID"
                            ]
            # Fetch data from the 'product_conn' database
            csv2_product_data = await self.db_query.join_tables(fields_list, table_names, join_condition,product_conn,"cscan_product.product_id",product_id)
            csv2_dataframe = pd.DataFrame(csv2_product_data).fillna(0) 
            if "afinity_id" in csv2_dataframe.columns and csv2_dataframe['afinity_id'].unique().any() != 0:
                afinity=await self.model.get_affinity_category(master_lookup_conn,csv2_dataframe) 
                affinity_category_names = [item['AffinityCategoryName'] for item in afinity if item['AffinityCategoryName']]
                print("affinity_category_names",affinity_category_names)
                affinity_names = [item['affinityName'] for item in afinity]   
                combined_affinity_category_name=",".join(affinity_category_names) if affinity_category_names else ""
                combined_affinity_name = ",".join(affinity_names)  
                rawdata.update({
                    "AffinityCategoryName": combined_affinity_category_name,
                    "affinityName": combined_affinity_name
                })
                #rawdata.update(afinity[0])
            if "document_content_type" in csv2_dataframe.columns and  csv2_dataframe['document_content_type'].unique().any() != 0:           
                document_data=await self.model.get_cscan_document(csv2_dataframe) 
                if 'document_placement' in document_data:
                    rawdata['document_placement'] = document_data['document_placement']
                if 'document_content_type' in document_data:
                    rawdata['document_content_type'] = document_data['document_content_type']
            if "traffic_sources" in csv2_dataframe.columns and  csv2_dataframe['traffic_sources'].unique().any() != 0:  
                rawdata['traffic_sources']=csv2_dataframe['traffic_sources'].unique()[0]
                rawdata["traffic_site_count"]=floor(len(csv2_dataframe['traffic_sources'].unique()) / 56) + 1    
                                
            return csv2_dataframe ,rawdata  
        except Exception as e:
            self.logger.exception(e)
                  
class  UxMobileProcessor(MediaProcessor):
    async def process(self, product_conn,master_lookup_conn,product_id,mobile_digital_conn):
        # Implementation specific to Online Display processing
        try:
            rawdata={}
            fields_list=["cscan_product.approved_date","cscan_product.fee_product","cscan_product.prescription","cscan_product.offer_origin_id","cscan_product.is_prescreen","cscan_product.worksite_voluntary","cscan_product.mtype_id","cscan_product.mpanel_id","cscan_product.product_headline","cscan_product.entry_id","cscan_product.product_name",
                            "cscan_product.is_qa","cscan_banking.DebitCardMentioned","cscan_banking.MinimumDeposit","cscan_banking.BankingCardType As Card_id","cscan_product_face_mapping.face_id","cscan_product_term_mapping.term_id","cscan_energy.productID as energy","cscan_banking.productID as banking",
                            "cscan_product.affinity_association","cscan_banking.BankingRewardsProgramEmphasis","cscan_banking.BankingRewardsProgram","cscan_payment_cards.*","cscan_energy.ERateType","cscan_energy.EOfferPrice","cscan_energy.ECancelFee","cscan_retail.RCreditCardMentioned","cscan_travel_leisure.productID as travel","cscan_credit_access_checks.productID as credit",
                            "cscan_document_text_search.dts_val","cscan_product.socialmedia_adtype","cscan_product_agent_communication_mapping.agent_communication_id","cscan_telecom.*","cscan_product_language_mapping.language_id","cscan_credit_access_checks.*","cscan_mortgage_loan.productID as mortgage_loan",
                            "cscan_travel_leisure.TLDebitCardMentioned","cscan_travel_leisure.TLCreditCardMentioned","cscan_travel_leisure.TLCardNetwork","cscan_product_state_mapping.state_id","cscan_ancillary_product_mapping.ancillary_product_id","cscan_telecom.productID as telecom","cscan_payment_cards.productID as payment",
                            "cscan_product.added_to_database","cscan_panelists_product.panelist_id","cscan_panelists_product.ppeve","cscan_product_age_mapping.age_id","cscan_product_incometype_mapping.income_id","cscan_product_affinity_mapping.afinity_id","cscan_product_riders_mapping.rider_id",
                            "cscan_panelists_product.ppage","cscan_panelists_product.ppdate","cscan_mortgage_loan.*","cscan_panelists_product.fico_range_id","cscan_panelists_product.vantage_range_id","cscan_panelists_product.creditVision_range_id","cscan_product_groupsize_mapping.group_size_id"
                            ,"cscan_product_electronic_mapping.electronic_id","cscan_document.document_content_type","cscan_document.document_placement","cscan_product_info.traffic_sources"]
            table_names = ["cscan_product", "cscan_product_agent_communication_mapping","cscan_document_text_search","cscan_banking","cscan_payment_cards","cscan_energy",
                        "cscan_retail","cscan_telecom","cscan_travel_leisure","cscan_product_state_mapping","cscan_panelists_product","cscan_product_age_mapping","cscan_product_incometype_mapping","cscan_product_language_mapping",
                        "cscan_ancillary_product_mapping","cscan_mortgage_loan","cscan_product_groupsize_mapping","cscan_product_affinity_mapping","cscan_product_face_mapping",
                        "cscan_product_riders_mapping","cscan_product_term_mapping","cscan_credit_access_checks","cscan_product_electronic_mapping","cscan_document","cscan_product_info"]
            join_condition=["cscan_product.product_id=cscan_product_agent_communication_mapping.product_id","cscan_product.product_id=cscan_document_text_search.productID",
                            "cscan_product.product_id=cscan_banking.productID","cscan_product.product_id=cscan_payment_cards.productID"
                            ,"cscan_product.product_id=cscan_energy.productID","cscan_product.product_id=cscan_retail.productID","cscan_product.product_id=cscan_telecom.productID"
                            ,"cscan_product.product_id=cscan_travel_leisure.productID","cscan_product.product_id=cscan_product_state_mapping.product_id","cscan_product.product_id=cscan_panelists_product.productID",
                            "cscan_product.product_id=cscan_product_age_mapping.product_id","cscan_product.product_id=cscan_product_incometype_mapping.product_id","cscan_product.product_id = cscan_product_language_mapping.product_id",
                            "cscan_product.product_id=cscan_ancillary_product_mapping.product_id","cscan_product.product_id=cscan_mortgage_loan.productID","cscan_product.product_id=cscan_product_groupsize_mapping.product_id"
                            ,"cscan_product.product_id=cscan_product_affinity_mapping.product_id","cscan_product.product_id=cscan_product_face_mapping.product_id","cscan_product.product_id=cscan_product_riders_mapping.product_id"
                            ,"cscan_product.product_id=cscan_product_term_mapping.product_id","cscan_product.product_id=cscan_credit_access_checks.productID","cscan_product.product_id=cscan_product_electronic_mapping.product_id",
                            "cscan_product.product_id=cscan_document.productID","cscan_product.product_id=cscan_product_info.product_id"]
            csv2_product_data = await self.db_query.join_tables(fields_list, table_names, join_condition,product_conn,"cscan_product.product_id",product_id)
            csv2_dataframe = pd.DataFrame(csv2_product_data).fillna(0) 
            if "afinity_id" in csv2_dataframe.columns and csv2_dataframe['afinity_id'].unique().any() != 0:
                afinity=await self.model.get_affinity_category(master_lookup_conn,csv2_dataframe) 
                affinity_category_names = [item['AffinityCategoryName'] for item in afinity if item['AffinityCategoryName']]
                print("affinity_category_names",affinity_category_names)
                affinity_names = [item['affinityName'] for item in afinity]   
                combined_affinity_category_name=",".join(affinity_category_names) if affinity_category_names else ""
                combined_affinity_name = ",".join(affinity_names)  
                rawdata.update({
                    "AffinityCategoryName": combined_affinity_category_name,
                    "affinityName": combined_affinity_name
                })
            if "document_content_type" in csv2_dataframe.columns and csv2_dataframe['document_content_type'].unique().any() != 0:         
                doc=await self.model.get_cscan_document(csv2_dataframe) 
                rawdata.update(doc)
            
            if 'panelist_id' in csv2_dataframe.columns and  csv2_dataframe['panelist_id'].unique().any() != 0:
                
                panalist=await self.model.get_panalist_eve(master_lookup_conn,csv2_dataframe)  
                print('panelist',panalist)   
                rawdata['panelist_details']=panalist
                edc=await self.model.getedc(csv2_dataframe,master_lookup_conn)
                rawdata['edc_name'] = edc
            rawdata['electronic_id']=int(csv2_dataframe['electronic_id'].unique()[0])   
            if "traffic_sources" in csv2_dataframe.columns and  csv2_dataframe['traffic_sources'].unique().any() != 0:  
                rawdata['traffic_sources']=csv2_dataframe['traffic_sources'].unique()[0]
                rawdata["traffic_site_count"]=floor(len(csv2_dataframe['traffic_sources'].unique()) / 56) + 1        
            return csv2_dataframe ,rawdata 
        except Exception as e:
            self.logger.exception(e)


class  UxDesktopProcessor(MediaProcessor):
    async def process(self, product_conn,master_lookup_conn,product_id,mobile_digital_conn):
        # Implementation specific to Online Display processing
        try:
            rawdata={}
            fields_list=["cscan_product.approved_date","cscan_product.fee_product","cscan_product.prescription","cscan_product.offer_origin_id","cscan_product.is_prescreen","cscan_product.worksite_voluntary","cscan_product.mtype_id","cscan_product.mpanel_id","cscan_product.product_headline","cscan_product.entry_id","cscan_product.product_name",
                            "cscan_product.is_qa","cscan_banking.DebitCardMentioned","cscan_banking.MinimumDeposit","cscan_banking.BankingCardType As Card_id","cscan_product_face_mapping.face_id","cscan_product_term_mapping.term_id","cscan_energy.productID as energy","cscan_banking.productID as banking",
                            "cscan_product.affinity_association","cscan_banking.BankingRewardsProgramEmphasis","cscan_banking.BankingRewardsProgram","cscan_payment_cards.*","cscan_energy.ERateType","cscan_energy.EOfferPrice","cscan_energy.ECancelFee","cscan_retail.RCreditCardMentioned","cscan_travel_leisure.productID as travel","cscan_credit_access_checks.productID as credit",
                            "cscan_document_text_search.dts_val","cscan_product.socialmedia_adtype","cscan_product_agent_communication_mapping.agent_communication_id","cscan_telecom.*","cscan_product_language_mapping.language_id","cscan_credit_access_checks.*","cscan_mortgage_loan.productID as mortgage_loan",
                            "cscan_travel_leisure.TLDebitCardMentioned","cscan_travel_leisure.TLCreditCardMentioned","cscan_travel_leisure.TLCardNetwork","cscan_product_state_mapping.state_id","cscan_ancillary_product_mapping.ancillary_product_id","cscan_telecom.productID as telecom","cscan_payment_cards.productID as payment",
                            "cscan_product.added_to_database","cscan_panelists_product.panelist_id","cscan_panelists_product.ppeve","cscan_product_age_mapping.age_id","cscan_product_incometype_mapping.income_id","cscan_product_affinity_mapping.afinity_id","cscan_product_riders_mapping.rider_id",
                            "cscan_panelists_product.ppage","cscan_panelists_product.ppdate","cscan_mortgage_loan.*","cscan_panelists_product.fico_range_id","cscan_panelists_product.vantage_range_id","cscan_panelists_product.creditVision_range_id","cscan_product_groupsize_mapping.group_size_id"
                            ,"cscan_product_electronic_mapping.electronic_id","cscan_document.document_content_type","cscan_document.document_placement","cscan_product_info.traffic_sources"]
            table_names = ["cscan_product", "cscan_product_agent_communication_mapping","cscan_document_text_search","cscan_banking","cscan_payment_cards","cscan_energy",
                        "cscan_retail","cscan_telecom","cscan_travel_leisure","cscan_product_state_mapping","cscan_panelists_product","cscan_product_age_mapping","cscan_product_incometype_mapping","cscan_product_language_mapping",
                        "cscan_ancillary_product_mapping","cscan_mortgage_loan","cscan_product_groupsize_mapping","cscan_product_affinity_mapping","cscan_product_face_mapping",
                        "cscan_product_riders_mapping","cscan_product_term_mapping","cscan_credit_access_checks","cscan_product_electronic_mapping","cscan_document","cscan_product_info"]
            join_condition=["cscan_product.product_id=cscan_product_agent_communication_mapping.product_id","cscan_product.product_id=cscan_document_text_search.productID",
                            "cscan_product.product_id=cscan_banking.productID","cscan_product.product_id=cscan_payment_cards.productID"
                            ,"cscan_product.product_id=cscan_energy.productID","cscan_product.product_id=cscan_retail.productID","cscan_product.product_id=cscan_telecom.productID"
                            ,"cscan_product.product_id=cscan_travel_leisure.productID","cscan_product.product_id=cscan_product_state_mapping.product_id","cscan_product.product_id=cscan_panelists_product.productID",
                            "cscan_product.product_id=cscan_product_age_mapping.product_id","cscan_product.product_id=cscan_product_incometype_mapping.product_id","cscan_product.product_id = cscan_product_language_mapping.product_id",
                            "cscan_product.product_id=cscan_ancillary_product_mapping.product_id","cscan_product.product_id=cscan_mortgage_loan.productID","cscan_product.product_id=cscan_product_groupsize_mapping.product_id"
                            ,"cscan_product.product_id=cscan_product_affinity_mapping.product_id","cscan_product.product_id=cscan_product_face_mapping.product_id","cscan_product.product_id=cscan_product_riders_mapping.product_id"
                            ,"cscan_product.product_id=cscan_product_term_mapping.product_id","cscan_product.product_id=cscan_credit_access_checks.productID","cscan_product.product_id=cscan_product_electronic_mapping.product_id",
                            "cscan_product.product_id=cscan_document.productID","cscan_product.product_id=cscan_product_info.product_id"]
            csv2_product_data = await self.db_query.join_tables(fields_list, table_names, join_condition,product_conn,"cscan_product.product_id",product_id)
            csv2_dataframe = pd.DataFrame(csv2_product_data).fillna(0) 
            if "afinity_id" in csv2_dataframe.columns and csv2_dataframe['afinity_id'].unique().any() != 0:
                afinity=await self.model.get_affinity_category(master_lookup_conn,csv2_dataframe) 
                affinity_category_names = [item['AffinityCategoryName'] for item in afinity if item['AffinityCategoryName']]
                print("affinity_category_names",affinity_category_names)
                affinity_names = [item['affinityName'] for item in afinity]   
                combined_affinity_category_name=",".join(affinity_category_names) if affinity_category_names else ""
                combined_affinity_name = ",".join(affinity_names)  
                rawdata.update({
                    "AffinityCategoryName": combined_affinity_category_name,
                    "affinityName": combined_affinity_name
                })
            if "document_content_type" in csv2_dataframe.columns and csv2_dataframe['document_content_type'].unique().any() != 0:         
                doc=await self.model.get_cscan_document(csv2_dataframe) 
                rawdata.update(doc)
            
            if 'panelist_id' in csv2_dataframe.columns and  csv2_dataframe['panelist_id'].unique().any() != 0:
                
                panalist=await self.model.get_panalist_eve(master_lookup_conn,csv2_dataframe)  
                print('panelist',panalist)   
                rawdata['panelist_details']=panalist
                edc=await self.model.getedc(csv2_dataframe,master_lookup_conn)
                rawdata['edc_name'] = edc
            rawdata['electronic_id']=int(csv2_dataframe['electronic_id'].unique()[0])   
            if "traffic_sources" in csv2_dataframe.columns and  csv2_dataframe['traffic_sources'].unique().any() != 0:  
                rawdata['traffic_sources']=csv2_dataframe['traffic_sources'].unique()[0]
                rawdata["traffic_site_count"]=floor(len(csv2_dataframe['traffic_sources'].unique()) / 56) + 1        
            return csv2_dataframe ,rawdata 
        except Exception as e:
            self.logger.exception(e)



class MediaProcessorFactory:
    @staticmethod
    def create_media_processor(media_channel):
        processors = {
                'Social Media': SocialMediaProcessor,
                'Email': EmailProcessor,
                'Print': PrintProcessor,
                'Dashboard': DashboardProcessor,
                'Direct Mail': DirectMailProcessor,
                'Search Engine Marketing': SearchEngineMarketingProcessor,
                'Online Video': OnlineVideoProcessor,
                'Website/URL': Website_URLProcessor,
                'Online Display': OnlineDisplayProcessor,
                'UX - Mobile': UxMobileProcessor,
                'UX - Desktop': UxDesktopProcessor,
                # Add more media channels as needed
            }
        processor_class = processors.get(media_channel)
        return processor_class() if processor_class else None  
            