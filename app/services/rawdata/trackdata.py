from query_functions.querys import Querys
from utils.Logger import Logger
import numpy as np


class TrackProcess:
    def __init__(self):
        self.logger =  Logger(self.__class__.__name__).get()
        self.query=Querys()

    async def fetch_track_data(self, tracking_id, product_conn):
        try:
            search_data = await self.query.updateemailtrack(tracking_id, product_conn)
            if search_data:  
                return {"Message": "Updated successfully"}
            else:
                return {"Message": f"Tracking ID not available"}
            
        except Exception as e:
            print("Error:", str(e))
            self.logger.exception(f"Error in fetch_track_data: {str(e)}")
            return {"Message": "Error occurred while fetching data"}
